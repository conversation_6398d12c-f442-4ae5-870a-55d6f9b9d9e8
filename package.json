{"name": "katrina-o3", "version": "1.0.0", "description": "Katrina AI 猎头机器人 - 专注 AI 算法职位招聘", "main": "src/index.js", "scripts": {"start": "node src/index.js", "dev": "nodemon src/index.js", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage"}, "keywords": ["ai", "recruitment", "chatbot", "headhunter", "job-matching"], "author": "Katrina-O3 Team", "license": "MIT", "dependencies": {"express": "^4.18.2", "dotenv": "^16.3.1", "pg": "^8.11.3", "winston": "^3.10.0", "axios": "^1.5.0", "@supabase/supabase-js": "^2.38.0"}, "devDependencies": {"jest": "^29.7.0", "supertest": "^6.3.3", "nodemon": "^3.0.1"}, "engines": {"node": ">=16.0.0"}, "jest": {"testEnvironment": "node", "setupFilesAfterEnv": ["<rootDir>/jest.setup.js"], "collectCoverageFrom": ["src/**/*.js", "!src/tests/**"], "testMatch": ["**/tests/**/*.test.js"], "testTimeout": 10000, "moduleNameMapper": {"^<rootDir>/src/(.*)$": "<rootDir>/src/$1"}}}