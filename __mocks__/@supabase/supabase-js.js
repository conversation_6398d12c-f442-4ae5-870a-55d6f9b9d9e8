/**
 * Supabase Mock for Testing
 * 模拟 Supabase 客户端的行为
 */

// 模拟数据
const mockUsers = [
  {
    id: 4,
    email: "<EMAIL>",
    user_type: "candidate",
    is_active: true,
  },
  {
    id: 5,
    email: "<EMAIL>",
    user_type: "candidate",
    is_active: true,
  },
  {
    id: 6,
    email: "<EMAIL>",
    user_type: "recruiter",
    is_active: true,
  },
];

const mockSessions = [];
const mockMessages = [];
const mockProfiles = [];
const mockJobs = [
  {
    id: 1,
    job_title: "NLP 算法工程师",
    location: "北京",
    salary_min: 300000,
    salary_max: 500000,
    companies: {
      id: 1,
      company_name: "阿里巴巴",
      company_type: "头部大厂",
      industry: "互联网",
    },
    is_active: true,
    priority_level: 5,
    created_at: "2024-01-01T00:00:00Z",
  },
  {
    id: 2,
    job_title: "CV 算法专家",
    location: "深圳",
    salary_min: 400000,
    salary_max: 600000,
    companies: {
      id: 2,
      company_name: "腾讯",
      company_type: "头部大厂",
      industry: "互联网",
    },
    is_active: true,
    priority_level: 4,
    created_at: "2024-01-02T00:00:00Z",
  },
];

const mockTechDirections = [
  { id: 1, tech_name: "NLP", keywords: "nlp,自然语言处理,文本", level: 1 },
  { id: 2, tech_name: "CV", keywords: "cv,计算机视觉,图像", level: 1 },
  { id: 3, tech_name: "推荐算法", keywords: "推荐,recommendation", level: 1 },
];

// 创建查询构建器 Mock
const createQueryBuilder = (tableName, mockData) => {
  let query = {
    data: [...mockData],
    filters: {},
    limit: null,
    single: false,
    orderBy: null,
    operation: "select",
    insertedData: null,
  };

  // 执行查询的核心逻辑
  const executeQuery = () => {
    // 处理 insert 操作
    if (query.operation === "insert") {
      if (query.single) {
        return { data: query.insertedData, error: null };
      } else {
        return {
          data: Array.isArray(query.insertedData)
            ? query.insertedData
            : [query.insertedData],
          error: null,
        };
      }
    }

    let filteredData = [...query.data];

    // 应用过滤器
    Object.entries(query.filters).forEach(([column, filter]) => {
      if (column === "_or") return; // 跳过 OR 条件，简化处理

      filteredData = filteredData.filter((record) => {
        const value =
          record[column] ||
          record[column.split(".")[0]]?.[column.split(".")[1]];

        switch (filter.op) {
          case "eq":
            return value === filter.value;
          case "in":
            return filter.value.includes(value);
          case "gte":
            return value >= filter.value;
          case "lte":
            return value <= filter.value;
          case "ilike":
            return (
              value &&
              value
                .toLowerCase()
                .includes(filter.value.replace(/%/g, "").toLowerCase())
            );
          default:
            return true;
        }
      });
    });

    // 应用排序
    if (query.orderBy) {
      filteredData.sort((a, b) => {
        const aVal = a[query.orderBy.column];
        const bVal = b[query.orderBy.column];
        const result = aVal > bVal ? 1 : aVal < bVal ? -1 : 0;
        return query.orderBy.ascending ? result : -result;
      });
    }

    // 应用限制
    if (query.limit) {
      filteredData = filteredData.slice(0, query.limit);
    }

    // 处理 single
    if (query.single) {
      const result = filteredData.length > 0 ? filteredData[0] : null;
      return { data: result, error: result ? null : { code: "PGRST116" } };
    }

    return { data: filteredData, error: null };
  };

  const builder = {
    select: jest.fn((columns = "*") => {
      // 简单处理 select，实际应该根据 columns 过滤字段
      return builder;
    }),

    eq: jest.fn((column, value) => {
      query.filters[column] = { op: "eq", value };
      return builder;
    }),

    in: jest.fn((column, values) => {
      query.filters[column] = { op: "in", value: values };
      return builder;
    }),

    gte: jest.fn((column, value) => {
      query.filters[column] = { op: "gte", value };
      return builder;
    }),

    lte: jest.fn((column, value) => {
      query.filters[column] = { op: "lte", value };
      return builder;
    }),

    ilike: jest.fn((column, pattern) => {
      query.filters[column] = { op: "ilike", value: pattern };
      return builder;
    }),

    or: jest.fn((condition) => {
      query.filters._or = condition;
      return builder;
    }),

    order: jest.fn((column, options = {}) => {
      query.orderBy = { column, ascending: options.ascending !== false };
      return builder;
    }),

    limit: jest.fn((count) => {
      query.limit = count;
      return builder;
    }),

    single: jest.fn(() => {
      query.single = true;
      return builder;
    }),

    insert: jest.fn((data) => {
      query.operation = "insert";
      const newRecord = Array.isArray(data) ? data : [data];
      const insertedRecords = [];

      newRecord.forEach((record) => {
        const id = Math.max(...mockData.map((r) => r.id || 0)) + 1;
        const fullRecord = { id, ...record };
        mockData.push(fullRecord);
        insertedRecords.push(fullRecord);
      });

      query.insertedData = Array.isArray(data)
        ? insertedRecords
        : insertedRecords[0];
      return builder;
    }),

    update: jest.fn((data) => {
      // 简单的更新逻辑
      return builder;
    }),
  };

  // 让 builder 本身可以被 await，直接返回查询结果
  // 这是关键：当 await builder 时，直接执行查询
  builder.then = jest.fn((onResolve, onReject) => {
    try {
      const result = executeQuery();
      return onResolve
        ? Promise.resolve(onResolve(result))
        : Promise.resolve(result);
    } catch (error) {
      return onReject ? Promise.reject(onReject(error)) : Promise.reject(error);
    }
  });

  // 添加 catch 方法以完整支持 Promise 接口
  builder.catch = jest.fn((onReject) => {
    return builder.then(null, onReject);
  });

  // 让 builder 支持 await 语法
  builder[Symbol.toStringTag] = "Promise";

  return builder;
};

// 创建 Supabase 客户端 Mock
const createClient = jest.fn((url, key) => {
  return {
    from: jest.fn((tableName) => {
      let mockData;
      switch (tableName) {
        case "users":
          mockData = mockUsers;
          break;
        case "chat_sessions":
          mockData = mockSessions;
          break;
        case "chat_messages":
          mockData = mockMessages;
          break;
        case "candidate_profiles":
          mockData = mockProfiles;
          break;
        case "job_listings":
          mockData = mockJobs;
          break;
        case "tech_tree":
          mockData = mockTechDirections;
          break;
        default:
          mockData = [];
      }
      return createQueryBuilder(tableName, mockData);
    }),
  };
});

module.exports = {
  createClient,
};
