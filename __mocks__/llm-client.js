/**
 * LLM Client Mock for Testing
 * 模拟 LLM 客户端的行为
 */

// 模拟的 LLM 响应
const mockResponses = {
  greeting: {
    reply: "您好！我是 Katrina，专注于 AI 算法领域的猎头顾问。很高兴为您服务！",
    conversation_state: {
      phase: "collecting",
      missing_fields: ["current_company", "tech_direction", "level_or_grade", "expected_compensation", "current_city_or_expected_city", "business_scenario"],
      recommendation_count: 0
    }
  },
  
  company_info: {
    reply: "了解到您在阿里巴巴工作，这是一家很优秀的公司！请问您主要做哪个技术方向呢？比如 NLP、CV、推荐算法等？",
    extracted_info: {
      current_company: "阿里巴巴"
    },
    conversation_state: {
      phase: "collecting",
      missing_fields: ["tech_direction", "level_or_grade", "expected_compensation", "current_city_or_expected_city", "business_scenario"],
      recommendation_count: 0
    }
  },

  tech_direction: {
    reply: "NLP 是很热门的方向！请问您目前的职级是什么？比如高级工程师、专家等？",
    extracted_info: {
      tech_direction: "NLP"
    },
    conversation_state: {
      phase: "collecting",
      missing_fields: ["level_or_grade", "expected_compensation", "current_city_or_expected_city", "business_scenario"],
      recommendation_count: 0
    }
  },

  level_info: {
    reply: "P7 是很不错的级别！基于您的背景，我为您推荐几个优质职位。",
    extracted_info: {
      level_or_grade: "P7"
    },
    recommendation_request: {
      trigger_type: "first",
      criteria: {
        tech_direction_id: 1,
        company_types: ["头部大厂", "国企", "中型公司", "创业型公司"]
      }
    },
    conversation_state: {
      phase: "recommending",
      missing_fields: ["expected_compensation", "current_city_or_expected_city", "business_scenario"],
      recommendation_count: 1
    }
  }
};

class MockLLMClient {
  constructor() {
    this.provider = 'mock';
    this.requestCount = 0;
    this.totalCost = 0;
  }

  async generate(prompt, options = {}) {
    this.requestCount++;
    
    // 模拟延迟
    await new Promise(resolve => setTimeout(resolve, 100));

    // 根据提示词内容返回不同的响应
    let response;
    if (prompt.includes('你好') || prompt.includes('Hello')) {
      response = mockResponses.greeting;
    } else if (prompt.includes('阿里巴巴') || prompt.includes('阿里')) {
      response = mockResponses.company_info;
    } else if (prompt.includes('NLP') || prompt.includes('nlp')) {
      response = mockResponses.tech_direction;
    } else if (prompt.includes('P7') || prompt.includes('高级工程师')) {
      response = mockResponses.level_info;
    } else {
      // 默认响应
      response = {
        reply: "我理解了您的信息，让我为您提供更好的建议。",
        conversation_state: {
          phase: "collecting",
          missing_fields: [],
          recommendation_count: 0
        }
      };
    }

    const cost = 0.01; // 模拟成本
    this.totalCost += cost;

    return {
      content: JSON.stringify(response),
      model: 'mock-model',
      usage: {
        inputTokens: prompt.length / 4,
        outputTokens: JSON.stringify(response).length / 4,
        totalTokens: (prompt.length + JSON.stringify(response).length) / 4
      },
      cost,
      responseTime: 100
    };
  }

  parseJsonResponse(content) {
    try {
      return JSON.parse(content);
    } catch (error) {
      // 如果解析失败，返回一个默认的响应结构
      return {
        reply: content,
        conversation_state: {
          phase: "collecting",
          missing_fields: [],
          recommendation_count: 0
        }
      };
    }
  }

  getStats() {
    return {
      provider: this.provider,
      requestCount: this.requestCount,
      totalCost: this.totalCost,
      averageCostPerRequest: this.requestCount > 0 ? this.totalCost / this.requestCount : 0
    };
  }

  resetStats() {
    this.requestCount = 0;
    this.totalCost = 0;
  }

  switchProvider(newProvider) {
    this.provider = newProvider;
  }

  async healthCheck() {
    return {
      status: 'healthy',
      provider: this.provider,
      responseTime: 50,
      model: 'mock-model'
    };
  }
}

// 创建单例实例
const mockLLMClient = new MockLLMClient();

module.exports = mockLLMClient;
