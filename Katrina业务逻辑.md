# Katrina 业务逻辑说明书

> 本文件用于说明 **Katrina AI 猎头机器人** 的纯业务层逻辑，开发人员（例如 Claude-4、Claude-3.5 或其他模型）只需按照本说明实现功能，而不必关心技术选型或架构实现细节。可与《Katrina-O3 技术落地方案》配套使用。

---
## 1. 项目定位
Katrina 是一名拟人化 AI 猎头，专注 **AI 算法职位** 招聘，通过自然对话收集候选人信息并精准推荐职位，支持每日上千候选人并发访问。

核心目标：
1. **高命中率推荐** – 通过分阶段信息收集与两次筛选，显著提升职位匹配度。
2. **自然对话体验** – 口吻亲切、追问灵活，适应非套路回复与多轮对话（10–20 轮）。
3. **数据合规** – 所有职位与公司数据必须来源于内部数据库，只读不可写，不得虚构。

---
## 2. 主要功能模块

| 编号 | 模块 | 功能概要 |
|------|------|----------|
| 2.1 | 拟人化沟通 | Katrina 角色设定与话术模板；情绪与语境自适应；回答身份追问。 |
| 2.2 | 信息收集 | 多轮对话获取 6 大关键信息：所在公司、技术方向、职级/级别、期望薪酬、所在/期望城市、业务场景；支持"本人"或"代推荐"角色识别。 |
| 2.3 | 职位推荐 | 主动/被动推荐逻辑，含 4×4 公司类型规则、两阶段筛选、缺失类型补位策略。 |
| 2.4 | 邀请投递简历 | 在推荐完成后，引导候选人上传简历（本人或朋友）。 |

---
## 3. 详细业务流程

### 3.1 对话入口与开场
1. Katrina 发送固定开场白：自我介绍 + 简历上传按钮提示 + 邮箱分享提示。
2. 进入 **信息收集状态**。

### 3.2 信息收集逻辑
- **必收信息点（共 6 项）**
  1. `current_company`
  2. `tech_direction`
  3. `level_or_grade`
  4. `expected_compensation`
  5. `current_city_or_expected_city`
  6. `business_scenario`

- **触发第一次推荐的必要条件 (满足其一)**
  * A. `current_company + tech_direction + level_or_grade`
  * B. `current_company + tech_direction + expected_compensation`
  * C. `current_company + tech_direction + level_or_grade + expected_compensation`
  * 若 `level_or_grade` 不在标准映射表中，则必须满足 B。

- **触发第二次推荐的必要条件**
  在第一次推荐完成后，补齐 **业务场景** 与 **城市**（若缺），然后才可触发第二次推荐。

- **多轮追问**
  * 对未回答字段，采用不同层级的追问话术（示例见 §6）。
  * 追问次数建议 ≤3；若候选人仍拒答则记录为 `unknown` 并继续其他字段。

### 3.3 非套路回复处理
- **上下文记忆**：记录已收集字段与对话历史，避免重复提问。
- **智能识别**：从非结构化回答中抽取关键词，如"我做 NLP" → `tech_direction=NLP`。
- **推理与角色判定**：当候选人提到朋友/同事时，将 `candidate_role=agent`。

### 3.4 职位推荐逻辑

1. **主动推荐**
   * **第一次推荐**
     - 在满足触发条件后，查询数据库得到最大 200 条候选职位。
     - 应用 **4×4 公司类型规则**（见下）。
   * **第二次推荐**
     - 仅在第一次推荐已完成时触发。
     - 在第一次结果集基础上追加筛选：`city` & `business_scenario` & `expected_compensation`（若缺）。
     - 重新应用 4×4 规则并返回。

2. **被动推荐**
   * 当候选人主动询问特定公司类型（如"外企"）时触发。
   * 前置条件：仍需满足第一次推荐触发条件。
   * 若无匹配职位则回复："当前没有适合的[类型]职位。"

3. **4×4 公司类型规则**

| 首选顺序 | 默认列表 |
|----------|----------|
| A | 头部大厂 |
| B | 国企 |
| C | 中型公司 |
| D | 创业型公司 |

补位策略（依次替换缺失类型）：
- 缺 A → `[B, C, D, B]`
- 缺 B → `[A, C, D, A]`
- 缺 A&B → `[C, C, C, D]`
- 仅剩 D → `[D, D, D, D]`

### 3.5 邀请投递简历
- 职位列表呈现后，Katrina 统一尾句：
  > “您看这些职位是否有兴趣？可通过左下角上传最新简历，我会帮您快速推进！如果您是为朋友推荐，也可以上传他们的简历哦。”

---
## 4. 业务约束
1. **禁止虚构**：职位 & 公司信息必须源自数据库；不可臆造或修改字段。
2. **信息不足不得推荐**：在未满足触发条件前，任何主动或被动请求均返回 **引导式追问**。
3. **数据库只读**：仅允许 `SELECT` 查询；不允许 `INSERT/UPDATE/DELETE`。
4. **响应时效**：对话应在 3 秒内首字节响应，10 秒内完成。
5. **并发支持**：每日千人级并发需保持平均响应 <500 ms (API 层)；超时降级返回“稍后重试”。

---
## 5. 状态机（简略）

```mermaid
stateDiagram-v2
[*] --> CollectingInfo
CollectingInfo --> FirstRecommend: trigger_1st
FirstRecommend --> CollectingInfo: need_more_info
FirstRecommend --> SecondRecommend: trigger_2nd
SecondRecommend --> InviteResume
InviteResume --> [*]
```

- `CollectingInfo` 保存 `pending_fields[]`；每轮对话完成后更新。
- 状态转移由业务逻辑判定，不依赖外部事件。

---
## 6. 追问话术示例

| 字段 | 问 1 | 问 2 | 问 3 |
|------|------|------|------|
| 公司 | 您的所在公司麻烦告知一下？ | 您所在的公司方便说吗？ | 是不是公司比较敏感呢，您一直没有讲。 |
| 技术方向 | 目前主要做哪条技术方向？ | 您是做 NLP、CV 还是别的？ | 方便分享具体技术方向吗？ |
| 期望薪酬 | 对薪资有什么期望？ | 您心里大概的薪资区间是多少？ | 您的期望薪酬可以说下吗？ |
*其余字段同理，可扩展 N 组*

---
## 7. 失败与降级策略
- **信息长期缺失**：追问 3 次仍无答案 → 将字段标为 `unknown`，继续下一个字段。
- **数据库无匹配**：回复“当前数据库暂无匹配职位”。
- **模型超时**：返回“系统忙，请稍后再试”，并记录日志。

---
## 8. 数据字典参考
- **公司类型枚举**：{A: 头部大厂, B: 国企, C: 中型公司, D: 创业型公司}
- **字段映射**：业务字段与 `DATABASE_REFERENCE.md` 中表结构一一对应。（例如 `tech_direction` → `tech_tree.id`）。

---
## 9. 附：对话流程示例

1. **开场**
   Katrina：您好，我是 AI 领域的猎头 Katrina…
2. **候选人**：我在 XX 科技做 NLP，高级工程师，想看看上海的机会。
3. **追问薪酬+场景**
4. **候选人**：薪资 30K-40K，主要做金融科技。
5. **第一次推荐（4×4 列表）**
6. **候选人**：有没有外企？
7. **被动推荐尝试** → 无匹配 → 回复无匹配信息。
8. **邀请简历**

---

> **完成** – 以上内容即为《Katrina 业务逻辑》文档，可直接提供给开发模型（Claude-4、Claude-3.5 等）作为需求说明。