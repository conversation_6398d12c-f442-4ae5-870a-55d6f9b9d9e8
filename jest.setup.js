/**
 * Jest 全局设置
 * 配置测试环境和超时时间
 */

// 设置测试环境 - 必须在最顶部
process.env.NODE_ENV = "test";

// 统一 mock – 必须放在 setup 顶部
jest.mock("@supabase/supabase-js");

// 设置全局超时时间为 10 秒
jest.setTimeout(10000);

// 静默 console.log 输出（可选）
if (process.env.SILENT_TESTS === "true") {
  global.console = {
    ...console,
    log: jest.fn(),
    info: jest.fn(),
    warn: jest.fn(),
    error: jest.fn(),
  };
}

// 全局测试前置设置
beforeAll(() => {
  // 可以在这里添加全局的测试前置逻辑
});

// 全局测试后置清理
afterAll(() => {
  // 可以在这里添加全局的测试后置清理逻辑
});
