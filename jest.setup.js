/**
 * Jest 全局设置
 * 配置测试环境和超时时间
 */

// 设置测试环境
process.env.NODE_ENV = "test";

// 设置全局超时时间为 10 秒
jest.setTimeout(10000);

// 在测试环境中启用 Mock
if (process.env.NODE_ENV === "test") {
  // Mock Supabase
  jest.mock("@supabase/supabase-js");

  // Mock LLM Client（仅在集成测试中）
  if (process.env.MOCK_LLM === "true") {
    jest.mock("./src/llm-client");
  }
}

// 静默 console.log 输出（可选）
if (process.env.SILENT_TESTS === "true") {
  global.console = {
    ...console,
    log: jest.fn(),
    info: jest.fn(),
    warn: jest.fn(),
    error: jest.fn(),
  };
}

// 全局测试前置设置
beforeAll(() => {
  // 可以在这里添加全局的测试前置逻辑
});

// 全局测试后置清理
afterAll(() => {
  // 可以在这里添加全局的测试后置清理逻辑
});
