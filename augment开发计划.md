# Katrina-O3 Augment 开发计划

> 基于第一阶段验收成功，结合《Katrina-O3.md》技术方案和《Katrina业务逻辑.md》需求文档制定的完整开发计划
> 
> **开发原则**: 严格按需求实现，不走捷径，每个功能都有完整测试覆盖

---

## 第一阶段回顾 ✅

**已完成项目**:
- ✅ 基础架构搭建 (Express + Supabase + LLM Client)
- ✅ 核心文件结构 (11个核心文件)
- ✅ Mock 测试体系 (28/28 测试通过)
- ✅ 基础对话功能
- ✅ 健康检查和统计接口

**技术债务**:
- 代码质量需要提升 (ESLint, 规范)
- 业务逻辑不完整 (缺少完整的信息收集和推荐逻辑)
- 测试覆盖率需要扩展

---

## 第二阶段：核心业务逻辑实现

### 2.1 信息收集系统重构 🎯

**目标**: 实现完整的6大信息收集逻辑

#### 2.1.1 候选人信息模型完善
- [ ] **扩展 candidate_profiles 表结构**
  - [ ] 添加 `candidate_role` 字段 (self/agent)
  - [ ] 完善 6 大必收字段的数据结构
  - [ ] 添加信息收集状态跟踪
  - [ ] 创建数据库迁移脚本

- [ ] **信息提取器重构** (`info-extractor.js`)
  - [ ] 实现智能关键词识别 (NLP → tech_direction)
  - [ ] 添加角色判定逻辑 (朋友推荐 → candidate_role=agent)
  - [ ] 实现增量信息更新
  - [ ] 添加信息冲突检测和解决

#### 2.1.2 对话状态机实现
- [ ] **状态管理器** (`conversation-state.js` - 新文件)
  - [ ] 实现 CollectingInfo → FirstRecommend → SecondRecommend 状态流转
  - [ ] 维护 `pending_fields[]` 队列
  - [ ] 实现状态持久化到数据库
  - [ ] 添加状态回滚机制

- [ ] **追问逻辑引擎** (`follow-up-engine.js` - 新文件)
  - [ ] 实现分层追问话术 (问1/问2/问3)
  - [ ] 追问次数限制 (≤3次)
  - [ ] 字段标记为 `unknown` 的逻辑
  - [ ] 上下文记忆避免重复提问

### 2.2 职位推荐系统完善 🎯

**目标**: 实现完整的4×4推荐规则和两阶段筛选

#### 2.2.1 推荐引擎重构 (`recommendation.js`)
- [ ] **触发条件检查器**
  - [ ] 第一次推荐条件: A/B/C 三种组合
  - [ ] 第二次推荐条件: 补齐业务场景和城市
  - [ ] 被动推荐条件: 特定公司类型询问

- [ ] **4×4 公司类型规则引擎**
  - [ ] 实现默认排序 [头部大厂, 国企, 中型公司, 创业型公司]
  - [ ] 实现补位策略 (缺A→[B,C,D,B], 缺B→[A,C,D,A] 等)
  - [ ] 动态类型可用性检查
  - [ ] 结果集重排序逻辑

- [ ] **两阶段筛选器**
  - [ ] 第一阶段: 基础条件筛选 (最大200条)
  - [ ] 第二阶段: 城市+业务场景+薪酬筛选
  - [ ] SQL 查询优化
  - [ ] 结果缓存机制

#### 2.2.2 职位数据管理
- [ ] **职位数据完善**
  - [ ] 扩展 Mock 数据覆盖4种公司类型
  - [ ] 添加更多技术方向和城市数据
  - [ ] 实现职位数据的动态加载
  - [ ] 添加职位数据验证

### 2.3 Prompt 模板系统重构 🎯

**目标**: 实现单一 Prompt 来源，支持复杂对话逻辑

#### 2.3.1 Prompt 模板重构 (`prompt-template.js`)
- [ ] **模块化 Prompt 结构**
  - [ ] System 指令模块 (Katrina 角色设定)
  - [ ] Tools 定义模块 (JSON Schema)
  - [ ] Conversation 历史模块 (最近5-10轮)
  - [ ] Memory 结构化信息模块
  - [ ] Task 指令模块

- [ ] **动态 Prompt 生成**
  - [ ] 根据对话状态动态调整指令
  - [ ] 上下文窗口管理 (8K 限制)
  - [ ] 历史消息压缩策略
  - [ ] JSON Schema 验证

#### 2.3.2 LLM 响应处理优化
- [ ] **响应解析器** (`response-parser.js` - 新文件)
  - [ ] 严格的 JSON 格式验证
  - [ ] 错误响应的降级处理
  - [ ] 响应时间监控
  - [ ] 重试机制优化

---

## 第三阶段：系统稳定性与质量提升

### 3.1 代码质量与规范 🎯

#### 3.1.1 代码规范建立
- [ ] **ESLint 配置**
  - [ ] 安装和配置 ESLint
  - [ ] 修复现有代码的 lint 问题
  - [ ] 配置 IDE 集成
  - [ ] 添加自定义规则

- [ ] **Pre-commit Hook**
  - [ ] 安装 husky 和 lint-staged
  - [ ] 配置 pre-commit 检查
  - [ ] 配置 commit message 规范
  - [ ] 添加测试运行检查

#### 3.1.2 代码重构
- [ ] **文件长度控制**
  - [ ] 确保每个文件 < 300 行
  - [ ] 提取公共函数到 utils
  - [ ] 模块化复杂逻辑
  - [ ] 添加 JSDoc 注释

### 3.2 测试体系完善 🎯

#### 3.2.1 单元测试扩展
- [ ] **核心模块单元测试**
  - [ ] `info-extractor.js` 测试套件
  - [ ] `recommendation.js` 测试套件
  - [ ] `conversation-state.js` 测试套件
  - [ ] `follow-up-engine.js` 测试套件
  - [ ] `response-parser.js` 测试套件

#### 3.2.2 集成测试完善
- [ ] **业务流程测试**
  - [ ] 完整的6大信息收集流程测试
  - [ ] 两阶段推荐流程测试
  - [ ] 4×4规则引擎测试
  - [ ] 多轮对话状态管理测试

#### 3.2.3 测试流水线
- [ ] **测试环境分离**
  - [ ] 纯 Mock 环境测试
  - [ ] 真实依赖连接测试
  - [ ] 性能测试套件
  - [ ] 压力测试配置

---

## 第四阶段：生产环境准备

### 4.1 性能与监控 🎯

#### 4.1.1 性能优化
- [ ] **响应时间优化**
  - [ ] 数据库查询优化
  - [ ] LLM 调用并发控制
  - [ ] 缓存策略实现
  - [ ] 内存使用优化

#### 4.1.2 监控与日志
- [ ] **监控指标**
  - [ ] API 响应时间监控
  - [ ] 错误率统计
  - [ ] 并发用户数监控
  - [ ] 资源使用监控

### 4.2 安全与部署 🎯

#### 4.2.1 安全加固
- [ ] **API 安全**
  - [ ] 请求限流实现
  - [ ] 输入验证加强
  - [ ] 敏感信息脱敏
  - [ ] CORS 策略配置

#### 4.2.2 部署准备
- [ ] **环境配置**
  - [ ] 生产环境配置完善
  - [ ] 环境变量验证
  - [ ] 日志轮转配置
  - [ ] 健康检查完善

---

## 开发时间线

### Week 1-2: 核心业务逻辑实现
- **Week 1**: 信息收集系统重构
- **Week 2**: 职位推荐系统完善

### Week 3: Prompt 系统与响应处理
- **Week 3**: Prompt 模板重构 + LLM 响应优化

### Week 4: 质量提升
- **Week 4**: 代码规范 + 测试完善

### Week 5: 生产准备
- **Week 5**: 性能优化 + 安全加固 + 部署准备

---

## 验收标准

### 功能验收
- [ ] 完整的6大信息收集流程
- [ ] 准确的4×4推荐规则实现
- [ ] 两阶段推荐逻辑正确
- [ ] 多轮对话状态管理稳定

### 质量验收
- [ ] 代码覆盖率 > 90%
- [ ] 所有 ESLint 规则通过
- [ ] API 响应时间 < 500ms
- [ ] 并发1000用户无异常

### 业务验收
- [ ] 完整对话流程演示
- [ ] 边缘案例处理正确
- [ ] 降级策略有效
- [ ] 数据合规检查通过

---

**承诺**: 严格按照业务逻辑文档实现，不虚构数据，不走捷径，每个功能都有完整测试覆盖。
