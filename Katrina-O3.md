# Katrina-O3 技术落地方案（无向量检索版）

> 面向 **qwen-turbo** 首发，保持可插拔，后续替换 **Claude-4-Sonnet / Claude-3.5-Sonnet** 仅需改动一个配置项。

---

## 1. 设计原则

1. **单一 Prompt 来源**：所有系统指令、角色设定、工具说明统一在 _prompt-template.js_，避免多套 Prompt 互相干扰。
2. **无向量检索**：仅使用关系型查询 + 规则过滤，保证结果可解释、易 Debug。
3. **上下文窗节制**：每次推理只携带最近 **5-10** 条消息 + 结构化关键信息（JSON），确保小上下文模型亦可工作。
4. **最小文件集**：核心逻辑 **10 个 JS 文件** ＋ 1 份测试，任何单文件 < 300 行，Claude-3.5 能完整解析。
5. **可热插拔 LLM**：`llm-client.js` 暴露统一接口 `generate(prompt, opts)`，后续替换供应商无需改业务代码。

---

## 2. 参考成熟方案

| 场景              | 可直接借鉴项目                | 链接                                                   | 适配说明                     |
| ----------------- | ----------------------------- | ------------------------------------------------------ | ---------------------------- |
| 对话式招聘机器人  | lagent-chat / R2 (Apache-2.0) | github.com/lionsoul2014/lagent-chat                    | 拆下 Prompt Router、FSM 原理 |
| 单体 Express + PG | supabase-tut (MIT)            | github.com/supabase-community/express-postgres-starter | 复用 PG 池、Env 处理         |
| LLM 封装层        | openai-tooling (Apache-2.0)   | github.com/transitive-bullshit/chatgpt-api             | 参考高并发重试逻辑           |
| 职位过滤规则引擎  | json-rules-engine (MIT)       | github.com/CacheControl/json-rules-engine              | 可做 4×4 推荐规则            |

> 以上组件均 MIT/Apache 许可证，可商业使用；文件数量精简、依赖轻量，Claude-3.5 可快速理解。

---

## 3. 文件架构一览（13 文件）

```text
src/
├─ index.js               # HTTP 入口，挂载 /chat、/health、/session 等 API
├─ config.js              # 环境变量与常量（LLM_PROVIDER, DB_URL）
├─ llm-client.js          # qwen / claude 适配层，统一 generate() 接口
├─ prompt-template.js     # Katrina Persona + System Prompt + JSON Schema
├─ message-handler.js     # Webhook 主逻辑，协调对话流程和状态管理
├─ info-extractor.js      # 解析 LLM JSON → 更新 candidate_profiles，支持冲突检测
├─ recommendation.js      # 4×4 推荐规则 & 两阶段 SQL 过滤
├─ database.js            # Supabase 客户端，参数化查询助手，会话管理
├─ logger.js              # winston 封装，业务事件记录
├─ utils.js               # 共用小工具（时间、校验、格式化）
├─ conversation/          # 对话管理模块
│  ├─ conversation-state.js  # 状态机：CollectingInfo → FirstRecommend → SecondRecommend
│  └─ follow-up-engine.js    # 追问逻辑：分层话术、次数限制、字段标记
└─ tests/
   ├─ integration.test.js    # Jest 端到端流程测试
   └─ info-collection.test.js # 信息收集系统单元测试
```

_总文件数：13_ （核心业务文件 11 个 + 对话管理模块 2 个）
每个文件 200-300 行上限，总代码量 ~3200 行，保持中型模型可读性。

**文件职责说明**：

- **HTTP 层**: `index.js` - API 路由和中间件
- **配置层**: `config.js` - 环境变量和常量管理
- **LLM 层**: `llm-client.js`, `prompt-template.js` - 模型调用和提示词管理
- **业务层**: `message-handler.js`, `info-extractor.js`, `recommendation.js` - 核心业务逻辑
- **数据层**: `database.js` - 数据库操作和会话管理
- **工具层**: `logger.js`, `utils.js` - 日志和通用工具
- **对话层**: `conversation/` - 状态管理和追问逻辑
- **测试层**: `tests/` - 单元测试和集成测试

---

## 4. 核心流程

1. **前端** `POST /chat` → `message-handler`
2. `message-handler`
   - 从 `chat_messages` 取最近 10 条
   - 拼装 `prompt-template` + 结构化上下文
   - 调用 `llm-client.generate`
3. LLM 返回 JSON： `{reply, extracted_info?, recommendation_request?}`
4. 若 `extracted_info` → `info-extractor` 更新 `candidate_profiles`
5. 若 `recommendation_request` → `recommendation` 执行 SQL，组合 4×4 结果
6. 最终回复文本发送给前端并记录到 `chat_messages`

---

## 5. Prompt 设计（单文件，分段注释）

```md
### system

你是 Katrina，一名 AI 猎头，… (安全/不虚构职位/禁止修改数据库)

### tools (JSON)

- search_jobs(company_type, tech_dir_id, …)
- update_profile(field, value)
  …

### conversation (last 5-10 turns)

{{conversation}}

### memory (structured)

{{profile_json}}

### task

根据以上信息，执行以下步骤 …
```

_提示_: 仅此一份模板，其他地方不得再硬编码任何追加提示。

---

## 6. FAQ & 换模型策略

| 问题                | 解决方案                                                         |
| ------------------- | ---------------------------------------------------------------- |
| 换 Claude-4 / 3.5？ | 修改 `config.js -> LLM_PROVIDER="claude"`，补充 KEY；Prompt 不变 |
| 上下文 8K 限制？    | `message-handler` 固定裁剪 10 条历史 + JSON Summaries            |
| 多轮追问乱序？      | FSM 内维护 `pending_fields` 队列，每次仅询问首位缺失信息         |

---

## 7. 下一步

1. **初始化仓库**: `pnpm init`, `pnpm add pg express dotenv winston`
2. **写 tests**: 使用 `supertest` 模拟对话三轮 → 断言回复格式
3. **灰度部署**: Render / Railway 单实例 → 日志观测

> 完毕。如需开始编码，可按以上文件架构逐步生成代码，Claude-4 / 3.5 均可承担编写与测试任务。
