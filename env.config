# AI招聘助手系统环境变量配置
# 请填入您的实际API密钥

# ==================== 数据库配置 ====================
SUPABASE_URL=https://qvcsywrwcfogxacryere.supabase.co
SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InF2Y3N5d3J3Y2ZvZ3hhY3J5ZXJlIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTEwMzM0MTksImV4cCI6MjA2NjYwOTQxOX0.eDlWWBxalaZMSqJ0uuiG8TCpD0WfWbdqYZfgl5Pm5do

# ==================== AI模型配置 ====================


# Qwen-Turbo 配置
QWEN_API_KEY=sk-0f243cb4b05a46359ad9a15f17cff400
QWEN_ENDPOINT=https://dashscope.aliyuncs.com/api/v1/services/aigc/text-generation/generation
QWEN_MODEL=qwen-turbo

# ==================== 系统配置 ====================
PORT=3000
NODE_ENV=development
DAILY_COST_LIMIT=50
COST_ALERT_THRESHOLD=0.8
CACHE_DIRECTORY=./cache/candidate_types
MAX_CACHE_SIZE=50000

# ==================== 候选人类型分析配置 ====================
# 缓存过期时间（天）
CACHE_EXPIRY_DAYS=14
# 行为模式变化阈值（0-1）
BEHAVIOR_CHANGE_THRESHOLD=0.4
# 置信度阈值（0-1）
CONFIDENCE_THRESHOLD=0.6
# 重新分析轮次间隔
ROUNDS_THRESHOLD=10
# 短回复比例阈值（0-1）
SHORT_REPLY_THRESHOLD=0.6
# 平均消息长度阈值（字符数）
AVG_MESSAGE_LENGTH_THRESHOLD=8
# 问题比例阈值（0-1）
QUESTION_RATIO_THRESHOLD=0.5
# 信息提供比例阈值（0-1）
INFO_PROVISION_THRESHOLD=0.2

# ==================== 安全配置 ====================
JWT_SECRET=your_jwt_secret_change_this_in_production
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# ==================== 日志配置 ====================
LOG_LEVEL=info
LOG_FILE_PATH=./logs/app.log
SUPABASE_SERVICE_ROLE_KEY=
SUPABASE_SERVICE_ROLE_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InF2Y3N5d3J3Y2ZvZ3hhY3J5ZXJlIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTEwMzM0MTksImV4cCI6MjA2NjYwOTQxOX0.eDlWWBxalaZMSqJ0uuiG8TCpD0WfWbdqYZfgl5Pm5do
