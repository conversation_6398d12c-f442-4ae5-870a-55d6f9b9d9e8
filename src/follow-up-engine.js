/**
 * 追问逻辑引擎
 * 实现分层追问话术、追问次数限制、字段标记为 unknown 的逻辑
 * 避免重复提问，维护上下文记忆
 */

const database = require('./database');
const logger = require('./logger');

// 最大追问次数
const MAX_FOLLOW_UP_COUNT = 3;

// 分层追问话术模板
const FOLLOW_UP_TEMPLATES = {
  current_company: [
    "您目前在哪家公司工作呢？",
    "方便透露一下您所在的公司吗？", 
    "您的公司信息比较敏感吗？如果不方便说可以跳过。"
  ],
  tech_direction: [
    "您主要做哪个技术方向呢？比如NLP、CV、推荐算法等？",
    "能具体说说您的技术专长吗？",
    "您的技术方向可以简单介绍下吗？"
  ],
  level_or_grade: [
    "您目前的职级是什么呢？比如高级工程师、专家等？",
    "能说说您的职业级别吗？",
    "您的职级信息方便分享吗？"
  ],
  expected_compensation: [
    "您对薪资有什么期望呢？",
    "您心里大概的薪资区间是多少？",
    "期望薪酬可以大致说一下吗？"
  ],
  current_city_or_expected_city: [
    "您目前在哪个城市工作？期望在哪里发展呢？",
    "工作地点有什么偏好吗？",
    "城市方面有什么要求吗？"
  ],
  business_scenario: [
    "您主要做什么业务场景呢？比如金融、电商、教育等？",
    "能说说您的业务领域吗？",
    "您的工作主要应用在哪个行业？"
  ]
};

// 字段的友好名称
const FIELD_DISPLAY_NAMES = {
  current_company: '公司信息',
  tech_direction: '技术方向',
  level_or_grade: '职级信息', 
  expected_compensation: '期望薪酬',
  current_city_or_expected_city: '工作城市',
  business_scenario: '业务场景'
};

class FollowUpEngine {
  constructor() {
    this.maxFollowUpCount = MAX_FOLLOW_UP_COUNT;
    this.templates = FOLLOW_UP_TEMPLATES;
    this.fieldNames = FIELD_DISPLAY_NAMES;
  }

  /**
   * 获取候选人的追问历史
   */
  async getFollowUpHistory(userId) {
    try {
      const profile = await database.getCandidateProfile(userId);
      return profile?.follow_up_counts || {};
    } catch (error) {
      logger.logError(error, { context: 'get_follow_up_history', userId });
      return {};
    }
  }

  /**
   * 更新追问次数
   */
  async updateFollowUpCount(userId, field) {
    try {
      const currentCounts = await this.getFollowUpHistory(userId);
      const newCount = (currentCounts[field] || 0) + 1;
      
      const updatedCounts = {
        ...currentCounts,
        [field]: newCount
      };

      await database.updateCandidateFollowUpCounts(userId, updatedCounts);
      
      logger.info('Follow-up count updated', { 
        userId, 
        field, 
        count: newCount 
      });

      return newCount;
    } catch (error) {
      logger.logError(error, { context: 'update_follow_up_count', userId, field });
      throw error;
    }
  }

  /**
   * 生成追问话术
   */
  async generateFollowUpQuestion(userId, field) {
    try {
      if (!this.templates[field]) {
        throw new Error(`No follow-up template for field: ${field}`);
      }

      const currentCount = await this.getFollowUpHistory(userId);
      const followUpCount = currentCount[field] || 0;

      // 检查是否超过最大追问次数
      if (followUpCount >= this.maxFollowUpCount) {
        logger.info('Max follow-up count reached', { userId, field, count: followUpCount });
        return null; // 不再追问
      }

      // 获取对应层级的话术
      const templates = this.templates[field];
      const templateIndex = Math.min(followUpCount, templates.length - 1);
      const question = templates[templateIndex];

      // 更新追问次数
      await this.updateFollowUpCount(userId, field);

      return {
        question,
        followUpCount: followUpCount + 1,
        isLastAttempt: followUpCount + 1 >= this.maxFollowUpCount
      };

    } catch (error) {
      logger.logError(error, { context: 'generate_follow_up_question', userId, field });
      throw error;
    }
  }

  /**
   * 标记字段为 unknown
   */
  async markFieldAsUnknown(userId, field, reason = 'max_follow_up_reached') {
    try {
      await database.updateCandidateFieldStatus(userId, field, 'unknown', reason);
      
      logger.info('Field marked as unknown', { 
        userId, 
        field, 
        reason 
      });

      return true;
    } catch (error) {
      logger.logError(error, { context: 'mark_field_unknown', userId, field });
      throw error;
    }
  }

  /**
   * 检查字段是否应该跳过（已达到最大追问次数）
   */
  async shouldSkipField(userId, field) {
    try {
      const followUpCounts = await this.getFollowUpHistory(userId);
      const count = followUpCounts[field] || 0;
      
      return count >= this.maxFollowUpCount;
    } catch (error) {
      logger.logError(error, { context: 'should_skip_field', userId, field });
      return false;
    }
  }

  /**
   * 生成字段收集提示
   */
  generateFieldPrompt(field, isFollowUp = false, followUpCount = 0) {
    const fieldName = this.fieldNames[field] || field;
    
    if (!isFollowUp) {
      return `请收集候选人的${fieldName}信息。`;
    }

    const templates = this.templates[field];
    if (!templates || followUpCount >= templates.length) {
      return `请继续询问候选人的${fieldName}信息。`;
    }

    return templates[followUpCount];
  }

  /**
   * 分析候选人回复是否包含所需信息
   */
  analyzeResponse(response, targetField) {
    // 简单的关键词匹配逻辑，后续可以用更复杂的NLP
    const keywords = {
      current_company: ['公司', '企业', '集团', '科技', '有限', '股份'],
      tech_direction: ['nlp', 'cv', '推荐', '算法', '机器学习', '深度学习', '人工智能'],
      level_or_grade: ['工程师', '专家', '总监', '经理', 'p6', 'p7', 'p8', 't6', 't7'],
      expected_compensation: ['k', '万', '薪资', '薪酬', '工资', '年薪'],
      current_city_or_expected_city: ['北京', '上海', '深圳', '杭州', '广州', '成都', '南京'],
      business_scenario: ['金融', '电商', '教育', '医疗', '游戏', '社交', '搜索', '推荐']
    };

    const fieldKeywords = keywords[targetField] || [];
    const lowerResponse = response.toLowerCase();
    
    return fieldKeywords.some(keyword => 
      lowerResponse.includes(keyword.toLowerCase())
    );
  }

  /**
   * 处理候选人拒绝回答
   */
  handleRefusal(response) {
    const refusalKeywords = [
      '不方便', '不想说', '跳过', '下一个', '不告诉', 
      '保密', '敏感', '不便透露', '暂时不说'
    ];

    const lowerResponse = response.toLowerCase();
    return refusalKeywords.some(keyword => 
      lowerResponse.includes(keyword)
    );
  }

  /**
   * 生成收集完成的确认信息
   */
  generateCollectionSummary(collectedFields) {
    const summary = Object.entries(collectedFields)
      .filter(([field, value]) => value && value !== 'unknown')
      .map(([field, value]) => {
        const fieldName = this.fieldNames[field] || field;
        return `${fieldName}: ${value}`;
      })
      .join('，');

    return `我已经了解到您的${summary}。`;
  }

  /**
   * 重置字段的追问历史
   */
  async resetFieldFollowUp(userId, field) {
    try {
      const currentCounts = await this.getFollowUpHistory(userId);
      const updatedCounts = {
        ...currentCounts,
        [field]: 0
      };

      await database.updateCandidateFollowUpCounts(userId, updatedCounts);
      
      logger.info('Field follow-up reset', { userId, field });
      return true;
    } catch (error) {
      logger.logError(error, { context: 'reset_field_follow_up', userId, field });
      throw error;
    }
  }
}

module.exports = new FollowUpEngine();
