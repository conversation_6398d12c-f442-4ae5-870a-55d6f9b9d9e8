/**
 * Katrina-O3 LLM 客户端模块
 * 支持 qwen-turbo 和 Claude，可热插拔切换
 */

const axios = require('axios');
const config = require('./config');
const logger = require('./logger');

class LLMClient {
  constructor() {
    this.provider = config.llm.provider;
    this.requestCount = 0;
    this.totalCost = 0;
  }

  /**
   * 统一的生成接口
   * @param {string} prompt - 输入提示词
   * @param {object} options - 可选参数
   * @returns {Promise<object>} 生成结果
   */
  async generate(prompt, options = {}) {
    try {
      this.requestCount++;
      const startTime = Date.now();

      let result;
      switch (this.provider) {
        case 'qwen':
          result = await this._generateQwen(prompt, options);
          break;
        case 'claude':
          result = await this._generateClaude(prompt, options);
          break;
        default:
          throw new Error(`Unsupported LLM provider: ${this.provider}`);
      }

      const responseTime = Date.now() - startTime;
      
      // 记录调用日志
      logger.logLLMCall(
        this.provider,
        result.model,
        prompt,
        result.content,
        result.cost
      );

      logger.info(`LLM generation completed in ${responseTime}ms`);

      return {
        content: result.content,
        model: result.model,
        usage: result.usage,
        cost: result.cost,
        responseTime
      };

    } catch (error) {
      logger.logError(error, {
        provider: this.provider,
        promptLength: prompt.length,
        requestCount: this.requestCount
      });
      throw error;
    }
  }

  /**
   * Qwen-Turbo 生成实现
   */
  async _generateQwen(prompt, options) {
    const requestData = {
      model: config.llm.qwen.model,
      input: {
        messages: [
          {
            role: 'user',
            content: prompt
          }
        ]
      },
      parameters: {
        max_tokens: options.maxTokens || config.llm.maxTokens,
        temperature: options.temperature || config.llm.temperature,
        top_p: options.topP || 0.8,
        result_format: 'message'
      }
    };

    const headers = {
      'Authorization': `Bearer ${config.llm.qwen.apiKey}`,
      'Content-Type': 'application/json',
      'X-DashScope-SSE': 'disable'
    };

    try {
      const response = await axios.post(
        config.llm.qwen.endpoint,
        requestData,
        {
          headers,
          timeout: config.llm.timeout
        }
      );

      if (response.data.output && response.data.output.choices && response.data.output.choices.length > 0) {
        const choice = response.data.output.choices[0];
        const usage = response.data.usage || {};
        
        // 估算成本（Qwen-Turbo 大约 ¥0.008/1K tokens）
        const totalTokens = (usage.input_tokens || 0) + (usage.output_tokens || 0);
        const estimatedCost = (totalTokens / 1000) * 0.008;
        this.totalCost += estimatedCost;

        return {
          content: choice.message.content,
          model: config.llm.qwen.model,
          usage: {
            inputTokens: usage.input_tokens || 0,
            outputTokens: usage.output_tokens || 0,
            totalTokens: totalTokens
          },
          cost: estimatedCost
        };
      } else {
        throw new Error('Invalid response format from Qwen API');
      }

    } catch (error) {
      if (error.response) {
        const errorMsg = error.response.data?.message || error.response.data?.error || 'Unknown API error';
        throw new Error(`Qwen API error: ${errorMsg}`);
      } else if (error.code === 'ECONNABORTED') {
        throw new Error('Qwen API timeout');
      } else {
        throw new Error(`Qwen API request failed: ${error.message}`);
      }
    }
  }

  /**
   * Claude 生成实现（预留）
   */
  async _generateClaude(prompt, options) {
    const requestData = {
      model: config.llm.claude.model,
      max_tokens: options.maxTokens || config.llm.maxTokens,
      temperature: options.temperature || config.llm.temperature,
      messages: [
        {
          role: 'user',
          content: prompt
        }
      ]
    };

    const headers = {
      'Authorization': `Bearer ${config.llm.claude.apiKey}`,
      'Content-Type': 'application/json',
      'anthropic-version': '2023-06-01'
    };

    try {
      const response = await axios.post(
        config.llm.claude.endpoint,
        requestData,
        {
          headers,
          timeout: config.llm.timeout
        }
      );

      const usage = response.data.usage || {};
      
      // Claude 成本估算（Claude-3.5-Sonnet 大约 $0.003/1K input tokens, $0.015/1K output tokens）
      const inputCost = (usage.input_tokens || 0) / 1000 * 0.003;
      const outputCost = (usage.output_tokens || 0) / 1000 * 0.015;
      const totalCost = inputCost + outputCost;
      this.totalCost += totalCost;

      return {
        content: response.data.content[0].text,
        model: config.llm.claude.model,
        usage: {
          inputTokens: usage.input_tokens || 0,
          outputTokens: usage.output_tokens || 0,
          totalTokens: (usage.input_tokens || 0) + (usage.output_tokens || 0)
        },
        cost: totalCost
      };

    } catch (error) {
      if (error.response) {
        const errorMsg = error.response.data?.error?.message || 'Unknown API error';
        throw new Error(`Claude API error: ${errorMsg}`);
      } else if (error.code === 'ECONNABORTED') {
        throw new Error('Claude API timeout');
      } else {
        throw new Error(`Claude API request failed: ${error.message}`);
      }
    }
  }

  /**
   * 解析 JSON 响应
   * 尝试从 LLM 响应中提取 JSON 内容
   */
  parseJsonResponse(content) {
    try {
      // 尝试直接解析
      return JSON.parse(content);
    } catch (error) {
      // 尝试提取 JSON 代码块
      const jsonMatch = content.match(/```json\s*([\s\S]*?)\s*```/);
      if (jsonMatch) {
        try {
          return JSON.parse(jsonMatch[1]);
        } catch (parseError) {
          logger.warn('Failed to parse JSON from code block', { content: jsonMatch[1] });
        }
      }

      // 尝试提取花括号内容
      const braceMatch = content.match(/\{[\s\S]*\}/);
      if (braceMatch) {
        try {
          return JSON.parse(braceMatch[0]);
        } catch (parseError) {
          logger.warn('Failed to parse JSON from braces', { content: braceMatch[0] });
        }
      }

      logger.warn('Could not extract valid JSON from LLM response', { content });
      return null;
    }
  }

  /**
   * 获取统计信息
   */
  getStats() {
    return {
      provider: this.provider,
      requestCount: this.requestCount,
      totalCost: this.totalCost,
      averageCostPerRequest: this.requestCount > 0 ? this.totalCost / this.requestCount : 0
    };
  }

  /**
   * 重置统计信息
   */
  resetStats() {
    this.requestCount = 0;
    this.totalCost = 0;
  }

  /**
   * 切换 LLM 提供商
   */
  switchProvider(newProvider) {
    if (!['qwen', 'claude'].includes(newProvider)) {
      throw new Error(`Unsupported provider: ${newProvider}`);
    }
    
    const oldProvider = this.provider;
    this.provider = newProvider;
    
    logger.info(`LLM provider switched from ${oldProvider} to ${newProvider}`);
  }

  /**
   * 健康检查
   */
  async healthCheck() {
    try {
      const testPrompt = "Hello, this is a health check. Please respond with 'OK'.";
      const result = await this.generate(testPrompt, { maxTokens: 10 });
      
      return {
        status: 'healthy',
        provider: this.provider,
        responseTime: result.responseTime,
        model: result.model
      };
    } catch (error) {
      return {
        status: 'unhealthy',
        provider: this.provider,
        error: error.message
      };
    }
  }
}

// 创建单例实例
const llmClient = new LLMClient();

module.exports = llmClient;
