/**
 * Katrina-O3 HTTP 服务入口
 * Express 服务器，挂载 /chat 和 /health 路由
 */

const express = require("express");

const config = require("./config");
const database = require("./database");
const messageHandler = require("./message-handler");
const llmClient = require("./llm-client");
const logger = require("./logger");
const Utils = require("./utils");

class KatrinaServer {
  constructor() {
    this.app = express();
    this.server = null;
    this.isShuttingDown = false;
  }

  /**
   * 初始化服务器
   */
  async initialize() {
    try {
      // 初始化数据库连接
      await database.initialize();

      // 配置中间件
      this._setupMiddleware();

      // 配置路由
      this._setupRoutes();

      // 配置错误处理
      this._setupErrorHandling();

      logger.info("✅ Katrina server initialized successfully");
    } catch (error) {
      logger.logError(error, { context: "server_initialization" });
      throw error;
    }
  }

  /**
   * 启动服务器
   */
  async start() {
    try {
      await this.initialize();

      this.server = this.app.listen(config.server.port, () => {
        logger.info(`🚀 Katrina server started on port ${config.server.port}`);
        logger.info(`📝 Environment: ${config.server.nodeEnv}`);
        logger.info(`🤖 LLM Provider: ${config.llm.provider}`);
      });

      // 优雅关闭处理
      this._setupGracefulShutdown();
    } catch (error) {
      logger.logError(error, { context: "server_startup" });
      process.exit(1);
    }
  }

  /**
   * 配置中间件
   */
  _setupMiddleware() {
    // 简单的 CORS 配置
    this.app.use((req, res, next) => {
      res.header("Access-Control-Allow-Origin", "*");
      res.header(
        "Access-Control-Allow-Methods",
        "GET, POST, PUT, DELETE, OPTIONS"
      );
      res.header(
        "Access-Control-Allow-Headers",
        "Origin, X-Requested-With, Content-Type, Accept, Authorization"
      );

      if (req.method === "OPTIONS") {
        res.sendStatus(200);
      } else {
        next();
      }
    });

    // JSON 解析
    this.app.use(express.json({ limit: "10mb" }));
    this.app.use(express.urlencoded({ extended: true, limit: "10mb" }));

    // 请求日志
    this.app.use((req, res, next) => {
      const startTime = Date.now();

      res.on("finish", () => {
        const responseTime = Date.now() - startTime;
        logger.logRequest(req, res, responseTime);
      });

      next();
    });

    // 简单的速率限制（内存存储）
    const requestCounts = new Map();

    this.app.use("/api/", (req, res, next) => {
      const clientIp = req.ip || req.connection.remoteAddress;
      const now = Date.now();
      const windowMs = config.security.rateLimitWindowMs;
      const maxRequests = config.security.rateLimitMaxRequests;

      // 清理过期记录
      for (const [ip, data] of requestCounts.entries()) {
        if (now - data.resetTime > windowMs) {
          requestCounts.delete(ip);
        }
      }

      // 检查当前 IP 的请求次数
      const clientData = requestCounts.get(clientIp) || {
        count: 0,
        resetTime: now,
      };

      if (now - clientData.resetTime > windowMs) {
        clientData.count = 0;
        clientData.resetTime = now;
      }

      clientData.count++;
      requestCounts.set(clientIp, clientData);

      if (clientData.count > maxRequests) {
        return res.status(429).json({
          error: "Too many requests",
          message: "请求过于频繁，请稍后再试",
        });
      }

      next();
    });
  }

  /**
   * 配置路由
   */
  _setupRoutes() {
    // 健康检查
    this.app.get("/health", async (req, res) => {
      try {
        const dbHealth = await database.healthCheck();
        const llmHealth = await llmClient.healthCheck();

        const health = {
          status: "ok",
          timestamp: new Date().toISOString(),
          version: "1.0.0",
          services: {
            database: dbHealth,
            llm: llmHealth,
          },
        };

        const overallStatus =
          dbHealth.status === "ok" && llmHealth.status === "healthy"
            ? 200
            : 503;
        res.status(overallStatus).json(health);
      } catch (error) {
        logger.logError(error, { context: "health_check" });
        res.status(503).json({
          status: "error",
          message: "Health check failed",
          timestamp: new Date().toISOString(),
        });
      }
    });

    // 聊天接口
    this.app.post("/api/chat", async (req, res) => {
      try {
        const { message, sessionId, userId } = req.body;

        // 验证请求参数
        if (!message || typeof message !== "string") {
          return res.status(400).json({
            error: "Invalid message",
            message: "消息内容不能为空",
          });
        }

        if (!sessionId) {
          return res.status(400).json({
            error: "Missing sessionId",
            message: "会话ID不能为空",
          });
        }

        // 处理消息
        const response = await messageHandler.handleMessage(
          sessionId,
          message.trim(),
          userId
        );

        res.json({
          success: true,
          data: response,
          timestamp: new Date().toISOString(),
        });
      } catch (error) {
        logger.logError(error, {
          context: "chat_endpoint",
          body: req.body,
        });

        res.status(500).json({
          error: "Internal server error",
          message: "服务器内部错误，请稍后再试",
          timestamp: new Date().toISOString(),
        });
      }
    });

    // 获取会话状态
    this.app.get("/api/session/:sessionId", async (req, res) => {
      try {
        const { sessionId } = req.params;

        const sessionState = await messageHandler.getSessionState(sessionId);

        if (!sessionState) {
          return res.status(404).json({
            error: "Session not found",
            message: "会话不存在",
          });
        }

        res.json({
          success: true,
          data: sessionState,
          timestamp: new Date().toISOString(),
        });
      } catch (error) {
        logger.logError(error, {
          context: "session_endpoint",
          sessionId: req.params.sessionId,
        });

        res.status(500).json({
          error: "Internal server error",
          message: "获取会话状态失败",
        });
      }
    });

    // 重置会话
    this.app.post("/api/session/:sessionId/reset", async (req, res) => {
      try {
        const { sessionId } = req.params;

        const success = await messageHandler.resetSession(sessionId);

        if (!success) {
          return res.status(404).json({
            error: "Session not found",
            message: "会话不存在或重置失败",
          });
        }

        res.json({
          success: true,
          message: "会话已重置",
          timestamp: new Date().toISOString(),
        });
      } catch (error) {
        logger.logError(error, {
          context: "session_reset",
          sessionId: req.params.sessionId,
        });

        res.status(500).json({
          error: "Internal server error",
          message: "重置会话失败",
        });
      }
    });

    // LLM 统计信息
    this.app.get("/api/stats/llm", async (req, res) => {
      try {
        const stats = llmClient.getStats();

        res.json({
          success: true,
          data: stats,
          timestamp: new Date().toISOString(),
        });
      } catch (error) {
        logger.logError(error, { context: "llm_stats" });

        res.status(500).json({
          error: "Internal server error",
          message: "获取统计信息失败",
        });
      }
    });

    // 404 处理
    this.app.use((req, res) => {
      res.status(404).json({
        error: "Not found",
        message: "接口不存在",
        path: req.originalUrl,
      });
    });
  }

  /**
   * 配置错误处理
   */
  _setupErrorHandling() {
    // 全局错误处理中间件
    this.app.use((error, req, res, next) => {
      logger.logError(error, {
        context: "express_error_handler",
        url: req.url,
        method: req.method,
        body: req.body,
      });

      // 防止错误信息泄露
      const isDevelopment = config.server.nodeEnv === "development";

      res.status(error.status || 500).json({
        error: "Internal server error",
        message: isDevelopment ? error.message : "服务器内部错误",
        ...(isDevelopment && { stack: error.stack }),
      });
    });

    // 未捕获异常处理
    process.on("uncaughtException", (error) => {
      logger.logError(error, { context: "uncaught_exception" });

      if (!this.isShuttingDown) {
        this._gracefulShutdown("uncaughtException");
      }
    });

    // 未处理的 Promise 拒绝
    process.on("unhandledRejection", (reason, promise) => {
      logger.logError(new Error(`Unhandled Rejection: ${reason}`), {
        context: "unhandled_rejection",
        promise: promise.toString(),
      });
    });
  }

  /**
   * 配置优雅关闭
   */
  _setupGracefulShutdown() {
    const signals = ["SIGTERM", "SIGINT"];

    signals.forEach((signal) => {
      process.on(signal, () => {
        logger.info(`Received ${signal}, starting graceful shutdown...`);
        this._gracefulShutdown(signal);
      });
    });
  }

  /**
   * 优雅关闭
   */
  async _gracefulShutdown(signal) {
    if (this.isShuttingDown) {
      return;
    }

    this.isShuttingDown = true;

    try {
      logger.info("Starting graceful shutdown...");

      // 停止接受新连接
      if (this.server) {
        this.server.close(() => {
          logger.info("HTTP server closed");
        });
      }

      // 等待现有请求完成
      await Utils.delay(5000);

      logger.info("Graceful shutdown completed");
      process.exit(0);
    } catch (error) {
      logger.logError(error, { context: "graceful_shutdown" });
      process.exit(1);
    }
  }
}

// 创建并启动服务器
const server = new KatrinaServer();

// 如果直接运行此文件，启动服务器
if (require.main === module) {
  server.start().catch((error) => {
    console.error("Failed to start server:", error);
    process.exit(1);
  });
}

module.exports = server;
