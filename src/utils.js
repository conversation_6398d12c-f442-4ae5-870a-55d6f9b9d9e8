/**
 * Katrina-O3 通用工具函数模块
 * 提供各种辅助功能和数据处理工具
 */

const crypto = require('crypto');
const config = require('./config');

class Utils {
  /**
   * 生成 UUID
   */
  static generateUUID() {
    return crypto.randomUUID();
  }

  /**
   * 生成会话 ID
   */
  static generateSessionId() {
    return this.generateUUID();
  }

  /**
   * 验证邮箱格式
   */
  static isValidEmail(email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  }

  /**
   * 验证手机号格式（中国大陆）
   */
  static isValidPhone(phone) {
    const phoneRegex = /^1[3-9]\d{9}$/;
    return phoneRegex.test(phone);
  }

  /**
   * 格式化时间戳
   */
  static formatTimestamp(timestamp, format = 'YYYY-MM-DD HH:mm:ss') {
    const date = new Date(timestamp);
    
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    const hours = String(date.getHours()).padStart(2, '0');
    const minutes = String(date.getMinutes()).padStart(2, '0');
    const seconds = String(date.getSeconds()).padStart(2, '0');

    return format
      .replace('YYYY', year)
      .replace('MM', month)
      .replace('DD', day)
      .replace('HH', hours)
      .replace('mm', minutes)
      .replace('ss', seconds);
  }

  /**
   * 计算两个时间的差值（分钟）
   */
  static getTimeDifferenceInMinutes(startTime, endTime) {
    const start = new Date(startTime);
    const end = new Date(endTime);
    return Math.floor((end - start) / (1000 * 60));
  }

  /**
   * 深度克隆对象
   */
  static deepClone(obj) {
    if (obj === null || typeof obj !== 'object') {
      return obj;
    }
    
    if (obj instanceof Date) {
      return new Date(obj.getTime());
    }
    
    if (obj instanceof Array) {
      return obj.map(item => this.deepClone(item));
    }
    
    if (typeof obj === 'object') {
      const clonedObj = {};
      for (const key in obj) {
        if (obj.hasOwnProperty(key)) {
          clonedObj[key] = this.deepClone(obj[key]);
        }
      }
      return clonedObj;
    }
  }

  /**
   * 安全的 JSON 解析
   */
  static safeJsonParse(jsonString, defaultValue = null) {
    try {
      return JSON.parse(jsonString);
    } catch (error) {
      return defaultValue;
    }
  }

  /**
   * 安全的 JSON 字符串化
   */
  static safeJsonStringify(obj, defaultValue = '{}') {
    try {
      return JSON.stringify(obj);
    } catch (error) {
      return defaultValue;
    }
  }

  /**
   * 检查对象是否为空
   */
  static isEmpty(obj) {
    if (obj === null || obj === undefined) return true;
    if (typeof obj === 'string') return obj.trim().length === 0;
    if (Array.isArray(obj)) return obj.length === 0;
    if (typeof obj === 'object') return Object.keys(obj).length === 0;
    return false;
  }

  /**
   * 提取数字
   */
  static extractNumbers(text) {
    const numbers = text.match(/\d+/g);
    return numbers ? numbers.map(num => parseInt(num)) : [];
  }

  /**
   * 提取薪资范围
   */
  static extractSalaryRange(text) {
    // 匹配各种薪资表达方式
    const patterns = [
      /(\d+)[kK万]?[-~到至](\d+)[kK万]?/,  // 30k-50k, 30万-50万
      /(\d+)[-~到至](\d+)[kK万]?/,        // 30-50k, 30-50万
      /年薪(\d+)[kK万]?[-~到至](\d+)[kK万]?/, // 年薪30k-50k
      /月薪(\d+)[kK万]?[-~到至](\d+)[kK万]?/, // 月薪30k-50k
    ];

    for (const pattern of patterns) {
      const match = text.match(pattern);
      if (match) {
        let min = parseInt(match[1]);
        let max = parseInt(match[2]);
        
        // 处理 k 和万的单位
        if (text.includes('k') || text.includes('K')) {
          min *= 1000;
          max *= 1000;
        } else if (text.includes('万')) {
          min *= 10000;
          max *= 10000;
        }
        
        // 如果是月薪，转换为年薪
        if (text.includes('月薪')) {
          min *= 12;
          max *= 12;
        }
        
        return { min, max };
      }
    }

    // 单个数字的情况
    const singleNumber = text.match(/(\d+)[kK万]?/);
    if (singleNumber) {
      let value = parseInt(singleNumber[1]);
      
      if (text.includes('k') || text.includes('K')) {
        value *= 1000;
      } else if (text.includes('万')) {
        value *= 10000;
      }
      
      if (text.includes('月薪')) {
        value *= 12;
      }
      
      // 单个数字时，设置一个合理的范围
      return { 
        min: Math.floor(value * 0.8), 
        max: Math.floor(value * 1.2) 
      };
    }

    return null;
  }

  /**
   * 标准化城市名称
   */
  static standardizeCity(cityText) {
    const cityMappings = {
      '北京': ['北京', '帝都', 'BJ', 'Beijing'],
      '上海': ['上海', '魔都', 'SH', 'Shanghai'],
      '深圳': ['深圳', 'SZ', 'Shenzhen'],
      '杭州': ['杭州', 'HZ', 'Hangzhou'],
      '广州': ['广州', 'GZ', 'Guangzhou'],
      '成都': ['成都', 'CD', 'Chengdu'],
      '南京': ['南京', 'NJ', 'Nanjing'],
      '武汉': ['武汉', 'WH', 'Wuhan'],
      '西安': ['西安', 'XA', 'Xian'],
      '苏州': ['苏州', 'SZ', 'Suzhou']
    };

    const normalizedInput = cityText.toLowerCase().trim();
    
    for (const [standard, variants] of Object.entries(cityMappings)) {
      if (variants.some(variant => 
        normalizedInput.includes(variant.toLowerCase()) ||
        variant.toLowerCase().includes(normalizedInput)
      )) {
        return standard;
      }
    }

    return cityText; // 如果没有匹配，返回原文
  }

  /**
   * 计算字符串相似度（简单版本）
   */
  static calculateSimilarity(str1, str2) {
    if (!str1 || !str2) return 0;
    
    const longer = str1.length > str2.length ? str1 : str2;
    const shorter = str1.length > str2.length ? str2 : str1;
    
    if (longer.length === 0) return 1.0;
    
    const editDistance = this.getEditDistance(longer, shorter);
    return (longer.length - editDistance) / longer.length;
  }

  /**
   * 计算编辑距离
   */
  static getEditDistance(str1, str2) {
    const matrix = [];
    
    for (let i = 0; i <= str2.length; i++) {
      matrix[i] = [i];
    }
    
    for (let j = 0; j <= str1.length; j++) {
      matrix[0][j] = j;
    }
    
    for (let i = 1; i <= str2.length; i++) {
      for (let j = 1; j <= str1.length; j++) {
        if (str2.charAt(i - 1) === str1.charAt(j - 1)) {
          matrix[i][j] = matrix[i - 1][j - 1];
        } else {
          matrix[i][j] = Math.min(
            matrix[i - 1][j - 1] + 1,
            matrix[i][j - 1] + 1,
            matrix[i - 1][j] + 1
          );
        }
      }
    }
    
    return matrix[str2.length][str1.length];
  }

  /**
   * 检查是否满足推荐触发条件
   */
  static checkRecommendationTrigger(profile, triggerType = 'first') {
    if (!profile) return false;

    const hasField = (field) => {
      const value = profile[field];
      return value !== null && value !== undefined && value !== '';
    };

    if (triggerType === 'first') {
      // 检查第一次推荐的触发条件
      const triggers = config.business.firstRecommendationTriggers;
      
      return triggers.some(trigger => 
        trigger.every(field => hasField(field))
      );
    } else if (triggerType === 'second') {
      // 检查第二次推荐的触发条件
      const requiredFields = config.business.secondRecommendationRequiredFields;
      
      return requiredFields.every(field => hasField(field));
    }

    return false;
  }

  /**
   * 获取缺失的字段
   */
  static getMissingFields(profile) {
    if (!profile) return config.business.requiredFields;

    return config.business.requiredFields.filter(field => {
      const value = profile[field];
      return value === null || value === undefined || value === '';
    });
  }

  /**
   * 计算档案完整度
   */
  static calculateProfileCompleteness(profile) {
    if (!profile) return 0;

    const totalFields = config.business.requiredFields.length;
    const completedFields = totalFields - this.getMissingFields(profile).length;
    
    return Math.round((completedFields / totalFields) * 100);
  }

  /**
   * 延迟执行
   */
  static delay(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * 重试机制
   */
  static async retry(fn, maxAttempts = 3, delayMs = 1000) {
    let lastError;
    
    for (let attempt = 1; attempt <= maxAttempts; attempt++) {
      try {
        return await fn();
      } catch (error) {
        lastError = error;
        
        if (attempt < maxAttempts) {
          await this.delay(delayMs * attempt); // 指数退避
        }
      }
    }
    
    throw lastError;
  }

  /**
   * 限制字符串长度
   */
  static truncateString(str, maxLength, suffix = '...') {
    if (!str || str.length <= maxLength) return str;
    return str.substring(0, maxLength - suffix.length) + suffix;
  }

  /**
   * 清理和标准化文本
   */
  static cleanText(text) {
    if (!text) return '';
    
    return text
      .trim()
      .replace(/\s+/g, ' ')  // 多个空格替换为单个空格
      .replace(/[\r\n]+/g, ' ') // 换行符替换为空格
      .replace(/[^\w\s\u4e00-\u9fff]/g, ''); // 保留中英文、数字和空格
  }
}

module.exports = Utils;
