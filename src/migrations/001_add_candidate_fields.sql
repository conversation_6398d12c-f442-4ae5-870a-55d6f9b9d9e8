-- 数据库迁移脚本：添加候选人信息收集所需字段
-- 执行时间：2025-08-05
-- 目的：支持完整的6大信息收集逻辑

-- 1. 添加候选人角色字段 (self: 本人, agent: 代推荐)
ALTER TABLE candidate_profiles 
ADD COLUMN IF NOT EXISTS candidate_role VARCHAR(10) DEFAULT 'self' 
CHECK (candidate_role IN ('self', 'agent'));

-- 2. 添加城市相关字段
ALTER TABLE candidate_profiles 
ADD COLUMN IF NOT EXISTS current_city VARCHAR(100),
ADD COLUMN IF NOT EXISTS expected_city VARCHAR(100);

-- 3. 添加信息收集状态跟踪字段
ALTER TABLE candidate_profiles 
ADD COLUMN IF NOT EXISTS info_collection_status JSONB DEFAULT '{}',
ADD COLUMN IF NOT EXISTS pending_fields TEXT[] DEFAULT ARRAY[]::TEXT[];

-- 4. 添加追问次数记录
ALTER TABLE candidate_profiles 
ADD COLUMN IF NOT EXISTS follow_up_counts JSONB DEFAULT '{}';

-- 5. 添加推荐状态字段
ALTER TABLE candidate_profiles 
ADD COLUMN IF NOT EXISTS recommendation_status VARCHAR(20) DEFAULT 'not_started'
CHECK (recommendation_status IN ('not_started', 'first_completed', 'second_completed'));

-- 6. 为新字段添加索引
CREATE INDEX IF NOT EXISTS idx_candidate_profiles_role ON candidate_profiles(candidate_role);
CREATE INDEX IF NOT EXISTS idx_candidate_profiles_cities ON candidate_profiles(current_city, expected_city);
CREATE INDEX IF NOT EXISTS idx_candidate_profiles_recommendation_status ON candidate_profiles(recommendation_status);

-- 7. 添加注释
COMMENT ON COLUMN candidate_profiles.candidate_role IS '候选人角色：self=本人，agent=代推荐';
COMMENT ON COLUMN candidate_profiles.current_city IS '当前所在城市';
COMMENT ON COLUMN candidate_profiles.expected_city IS '期望工作城市';
COMMENT ON COLUMN candidate_profiles.info_collection_status IS '信息收集状态，记录各字段的收集情况';
COMMENT ON COLUMN candidate_profiles.pending_fields IS '待收集的字段列表';
COMMENT ON COLUMN candidate_profiles.follow_up_counts IS '各字段的追问次数记录';
COMMENT ON COLUMN candidate_profiles.recommendation_status IS '推荐状态：not_started, first_completed, second_completed';
