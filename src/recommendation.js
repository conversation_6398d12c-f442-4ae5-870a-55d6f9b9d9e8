/**
 * Katrina-O3 职位推荐引擎
 * 实现 4×4 公司类型推荐规则和 SQL 查询逻辑
 */

const database = require('./database');
const logger = require('./logger');
const Utils = require('./utils');
const config = require('./config');

class RecommendationEngine {
  constructor() {
    this.companyTypes = config.business.companyTypes;
    this.fallbackStrategies = config.business.fallbackStrategies;
    this.maxRecommendations = config.business.maxRecommendations;
  }

  /**
   * 执行职位推荐
   */
  async recommend(candidateProfile, recommendationType = 'first', specificCompanyType = null) {
    try {
      logger.logBusinessEvent('recommendation_started', {
        userId: candidateProfile.user_id,
        type: recommendationType,
        specificType: specificCompanyType
      });

      // 构建搜索条件
      const searchCriteria = await this._buildSearchCriteria(candidateProfile, recommendationType);
      
      if (!searchCriteria) {
        logger.warn('Cannot build search criteria', { candidateProfile });
        return { jobs: [], message: '信息不足，无法进行推荐' };
      }

      // 搜索职位
      const allJobs = await database.searchJobs(searchCriteria);
      
      if (!allJobs || allJobs.length === 0) {
        return { jobs: [], message: '暂时没有匹配的职位' };
      }

      // 应用推荐策略
      let recommendedJobs;
      if (specificCompanyType) {
        recommendedJobs = await this._applySpecificTypeRecommendation(allJobs, specificCompanyType);
      } else {
        recommendedJobs = await this._apply4x4Recommendation(allJobs);
      }

      // 格式化推荐结果
      const formattedJobs = this._formatRecommendations(recommendedJobs);

      logger.logBusinessEvent('recommendation_completed', {
        userId: candidateProfile.user_id,
        jobCount: formattedJobs.length,
        type: recommendationType
      });

      return {
        jobs: formattedJobs,
        message: this._generateRecommendationMessage(formattedJobs.length, recommendationType)
      };

    } catch (error) {
      logger.logError(error, { 
        userId: candidateProfile?.user_id,
        recommendationType,
        specificCompanyType 
      });
      throw error;
    }
  }

  /**
   * 构建搜索条件
   */
  async _buildSearchCriteria(profile, recommendationType) {
    const criteria = {
      limit: 200 // 获取更多候选职位用于筛选
    };

    // 技术方向（必需）
    if (profile.primary_tech_direction_id) {
      criteria.techDirectionId = profile.primary_tech_direction_id;
    } else {
      logger.warn('Missing tech direction for recommendation', { userId: profile.user_id });
      return null;
    }

    // 薪资范围
    if (profile.expected_compensation_min && profile.expected_compensation_max) {
      criteria.salaryMin = profile.expected_compensation_min;
      criteria.salaryMax = profile.expected_compensation_max;
    }

    // 职级范围
    if (profile.candidate_standard_level_min && profile.candidate_standard_level_max) {
      criteria.levelMin = profile.candidate_standard_level_min;
      criteria.levelMax = profile.candidate_standard_level_max;
    }

    // 第二次推荐的额外条件
    if (recommendationType === 'second') {
      // 业务场景
      if (profile.primary_business_scenario_id) {
        criteria.businessScenarioId = profile.primary_business_scenario_id;
      }

      // 城市
      if (profile.desired_location_standard) {
        criteria.location = profile.desired_location_standard;
      }
    }

    return criteria;
  }

  /**
   * 应用 4×4 推荐策略
   */
  async _apply4x4Recommendation(allJobs) {
    // 按公司类型分组
    const jobsByType = this._groupJobsByCompanyType(allJobs);
    
    // 确定推荐策略
    const strategy = this._determineRecommendationStrategy(jobsByType);
    
    // 按策略选择职位
    const selectedJobs = [];
    
    for (let i = 0; i < this.maxRecommendations; i++) {
      const targetType = strategy[i];
      const typeJobs = jobsByType[targetType] || [];
      
      if (typeJobs.length > 0) {
        // 从该类型中选择最佳职位
        const selectedJob = this._selectBestJobFromType(typeJobs, selectedJobs);
        if (selectedJob) {
          selectedJobs.push(selectedJob);
        }
      }
    }

    return selectedJobs;
  }

  /**
   * 应用特定公司类型推荐
   */
  async _applySpecificTypeRecommendation(allJobs, specificType) {
    const jobsByType = this._groupJobsByCompanyType(allJobs);
    const targetJobs = jobsByType[specificType] || [];
    
    if (targetJobs.length === 0) {
      return [];
    }

    // 选择该类型的最佳职位
    const selectedJobs = [];
    const maxJobs = Math.min(this.maxRecommendations, targetJobs.length);
    
    for (let i = 0; i < maxJobs; i++) {
      const selectedJob = this._selectBestJobFromType(targetJobs, selectedJobs);
      if (selectedJob) {
        selectedJobs.push(selectedJob);
      }
    }

    return selectedJobs;
  }

  /**
   * 按公司类型分组职位
   */
  _groupJobsByCompanyType(jobs) {
    const grouped = {
      '头部大厂': [],
      '国企': [],
      '中型公司': [],
      '创业型公司': []
    };

    for (const job of jobs) {
      const companyType = job.companies?.company_type;
      if (companyType && grouped[companyType]) {
        grouped[companyType].push(job);
      }
    }

    return grouped;
  }

  /**
   * 确定推荐策略
   */
  _determineRecommendationStrategy(jobsByType) {
    const availableTypes = Object.keys(jobsByType).filter(type => 
      jobsByType[type] && jobsByType[type].length > 0
    );

    // 默认策略
    const defaultStrategy = ['头部大厂', '国企', '中型公司', '创业型公司'];
    
    // 检查缺失的类型
    const missingTypes = defaultStrategy.filter(type => !availableTypes.includes(type));
    
    if (missingTypes.length === 0) {
      return defaultStrategy;
    }

    // 应用补位策略
    if (missingTypes.includes('头部大厂') && missingTypes.includes('国企')) {
      return ['中型公司', '中型公司', '中型公司', '创业型公司'];
    } else if (missingTypes.includes('头部大厂')) {
      return ['国企', '中型公司', '创业型公司', '国企'];
    } else if (missingTypes.includes('国企')) {
      return ['头部大厂', '中型公司', '创业型公司', '头部大厂'];
    } else if (availableTypes.length === 1 && availableTypes[0] === '创业型公司') {
      return ['创业型公司', '创业型公司', '创业型公司', '创业型公司'];
    }

    // 其他情况，使用可用类型循环填充
    const strategy = [];
    for (let i = 0; i < this.maxRecommendations; i++) {
      strategy.push(availableTypes[i % availableTypes.length]);
    }

    return strategy;
  }

  /**
   * 从特定类型中选择最佳职位
   */
  _selectBestJobFromType(typeJobs, alreadySelected) {
    // 过滤已选择的职位
    const availableJobs = typeJobs.filter(job => 
      !alreadySelected.some(selected => selected.id === job.id)
    );

    if (availableJobs.length === 0) {
      return null;
    }

    // 按优先级排序
    availableJobs.sort((a, b) => {
      // 优先级高的在前
      if (a.priority_level !== b.priority_level) {
        return b.priority_level - a.priority_level;
      }
      
      // 创建时间新的在前
      return new Date(b.created_at) - new Date(a.created_at);
    });

    return availableJobs[0];
  }

  /**
   * 格式化推荐结果
   */
  _formatRecommendations(jobs) {
    return jobs.map(job => ({
      id: job.id,
      title: job.job_title,
      company: {
        id: job.companies.id,
        name: job.companies.company_name,
        type: job.companies.company_type,
        industry: job.companies.industry
      },
      location: job.location,
      salary: {
        min: job.salary_min,
        max: job.salary_max,
        display: this._formatSalary(job.salary_min, job.salary_max)
      },
      level: {
        raw: job.job_level_raw,
        min: job.job_standard_level_min,
        max: job.job_standard_level_max
      },
      experience: job.experience_required,
      description: Utils.truncateString(job.job_description, 200),
      requirements: Utils.truncateString(job.requirements, 150),
      benefits: Utils.truncateString(job.benefits, 100),
      priority: job.priority_level,
      createdAt: job.created_at
    }));
  }

  /**
   * 格式化薪资显示
   */
  _formatSalary(min, max) {
    if (!min && !max) return '面议';
    
    const formatAmount = (amount) => {
      if (amount >= 10000) {
        return `${(amount / 10000).toFixed(0)}万`;
      }
      return `${(amount / 1000).toFixed(0)}k`;
    };

    if (min && max) {
      return `${formatAmount(min)}-${formatAmount(max)}`;
    } else if (min) {
      return `${formatAmount(min)}+`;
    } else {
      return `最高${formatAmount(max)}`;
    }
  }

  /**
   * 生成推荐消息
   */
  _generateRecommendationMessage(jobCount, recommendationType) {
    if (jobCount === 0) {
      return '抱歉，暂时没有找到匹配的职位。您可以调整一下期望条件，我会继续为您寻找合适的机会。';
    }

    const typeText = recommendationType === 'second' ? '进一步筛选后' : '';
    
    return `${typeText}为您推荐了 ${jobCount} 个优质职位，都是根据您的背景精心匹配的。您可以详细了解一下，有感兴趣的我可以帮您对接。`;
  }

  /**
   * 检查是否可以进行推荐
   */
  canRecommend(profile, recommendationType = 'first') {
    if (!profile) return false;

    if (recommendationType === 'first') {
      return Utils.checkRecommendationTrigger(profile, 'first');
    } else if (recommendationType === 'second') {
      return Utils.checkRecommendationTrigger(profile, 'first') && 
             Utils.checkRecommendationTrigger(profile, 'second');
    }

    return false;
  }

  /**
   * 获取推荐统计信息
   */
  async getRecommendationStats(userId) {
    try {
      // TODO: 实现推荐统计功能
      // 可以记录用户的推荐历史、点击率等
      return {
        totalRecommendations: 0,
        clickRate: 0,
        favoriteCompanyTypes: []
      };
    } catch (error) {
      logger.logError(error, { userId });
      return null;
    }
  }
}

// 创建单例实例
const recommendationEngine = new RecommendationEngine();

module.exports = recommendationEngine;
