/**
 * 对话状态管理器
 * 实现 CollectingInfo → FirstRecommend → SecondRecommend 状态流转
 * 维护 pending_fields 队列和状态持久化
 *
 * @module ConversationState
 * <AUTHOR> Team
 * @version 1.0.0
 */

const database = require("../database");
const logger = require("../logger");
const config = require("../config");

// 对话状态枚举
const CONVERSATION_STATES = {
  COLLECTING_INFO: "collecting_info",
  FIRST_RECOMMEND: "first_recommend",
  SECOND_RECOMMEND: "second_recommend",
  INVITE_RESUME: "invite_resume",
  COMPLETED: "completed",
};

// 必收信息字段
const REQUIRED_FIELDS = [
  "current_company",
  "tech_direction",
  "level_or_grade",
  "expected_compensation",
  "current_city_or_expected_city",
  "business_scenario",
];

// 第一次推荐触发条件组合
const FIRST_RECOMMEND_CONDITIONS = [
  // 条件A: 公司 + 技术方向 + 职级
  ["current_company", "tech_direction", "level_or_grade"],
  // 条件B: 公司 + 技术方向 + 期望薪酬
  ["current_company", "tech_direction", "expected_compensation"],
  // 条件C: 公司 + 技术方向 + 职级 + 期望薪酬
  [
    "current_company",
    "tech_direction",
    "level_or_grade",
    "expected_compensation",
  ],
];

class ConversationStateManager {
  constructor() {
    this.states = CONVERSATION_STATES;
    this.requiredFields = REQUIRED_FIELDS;
  }

  /**
   * 获取会话的当前状态
   * @param {number} sessionId - 会话ID
   * @returns {Promise<Object>} 当前状态对象
   */
  async getCurrentState(sessionId) {
    const session = await database.getSessionById(sessionId);
    if (!session) throw new Error(`Session not found: ${sessionId}`);

    const context = session.current_interaction_context || {};
    return {
      phase: context.phase || this.states.COLLECTING_INFO,
      pendingFields: context.pending_fields || [...this.requiredFields],
      collectedFields: context.collected_fields || [],
      recommendationCount: context.recommendation_count || 0,
      lastUpdated: context.last_updated,
    };
  }

  /**
   * 更新会话状态
   * @param {number} sessionId - 会话ID
   * @param {Object} newState - 新状态数据
   * @returns {Promise<Object>} 更新后的状态
   */
  async updateState(sessionId, newState) {
    const currentContext = await this.getCurrentState(sessionId);
    const updatedContext = {
      ...currentContext,
      ...newState,
      last_updated: new Date().toISOString(),
    };

    await database.updateSessionContext(sessionId, updatedContext);
    logger.info("State updated", { sessionId, phase: updatedContext.phase });
    return updatedContext;
  }

  /**
   * 检查是否可以触发第一次推荐
   */
  canTriggerFirstRecommend(collectedFields) {
    return FIRST_RECOMMEND_CONDITIONS.some((condition) =>
      condition.every((field) => collectedFields.includes(field))
    );
  }

  /**
   * 检查是否可以触发第二次推荐
   */
  canTriggerSecondRecommend(collectedFields, recommendationCount) {
    // 必须已完成第一次推荐
    if (recommendationCount < 1) {
      return false;
    }

    // 必须补齐业务场景和城市信息
    const requiredForSecond = [
      "business_scenario",
      "current_city_or_expected_city",
    ];
    return requiredForSecond.every((field) => collectedFields.includes(field));
  }

  /**
   * 处理信息收集完成后的状态转移
   */
  async processFieldCollection(sessionId, newlyCollectedFields) {
    try {
      const currentState = await this.getCurrentState(sessionId);

      // 更新已收集字段
      const updatedCollectedFields = [
        ...new Set([...currentState.collectedFields, ...newlyCollectedFields]),
      ];

      // 更新待收集字段
      const updatedPendingFields = this.requiredFields.filter(
        (field) => !updatedCollectedFields.includes(field)
      );

      // 判断状态转移
      let newPhase = currentState.phase;
      let recommendationTrigger = null;

      if (currentState.phase === this.states.COLLECTING_INFO) {
        if (this.canTriggerFirstRecommend(updatedCollectedFields)) {
          newPhase = this.states.FIRST_RECOMMEND;
          recommendationTrigger = "first";
        }
      } else if (currentState.phase === this.states.FIRST_RECOMMEND) {
        if (
          this.canTriggerSecondRecommend(
            updatedCollectedFields,
            currentState.recommendationCount
          )
        ) {
          newPhase = this.states.SECOND_RECOMMEND;
          recommendationTrigger = "second";
        }
      }

      // 更新状态
      const updatedState = await this.updateState(sessionId, {
        phase: newPhase,
        collectedFields: updatedCollectedFields,
        pendingFields: updatedPendingFields,
      });

      return {
        state: updatedState,
        recommendationTrigger,
        shouldRecommend: recommendationTrigger !== null,
      };
    } catch (error) {
      logger.logError(error, {
        context: "process_field_collection",
        sessionId,
      });
      throw error;
    }
  }

  /**
   * 标记推荐完成
   * @param {number} sessionId - 会话ID
   * @param {string} recommendationType - 推荐类型 ('first'|'second')
   * @returns {Promise<Object>} 更新后的状态
   */
  async markRecommendationCompleted(sessionId, recommendationType) {
    const currentState = await this.getCurrentState(sessionId);
    const newRecommendationCount = recommendationType === "first" ? 1 : 2;
    const newPhase =
      recommendationType === "second" || currentState.pendingFields.length === 0
        ? this.states.INVITE_RESUME
        : this.states.COLLECTING_INFO;

    return await this.updateState(sessionId, {
      phase: newPhase,
      recommendation_count: newRecommendationCount,
    });
  }

  /**
   * 获取下一个需要收集的字段
   * @param {string[]} pendingFields - 待收集字段列表
   * @returns {string|null} 下一个字段名或null
   */
  getNextFieldToCollect(pendingFields) {
    const priority = [
      "current_company",
      "tech_direction",
      "level_or_grade",
      "expected_compensation",
      "current_city_or_expected_city",
      "business_scenario",
    ];
    return priority.find((field) => pendingFields.includes(field)) || null;
  }

  /**
   * 重置会话状态
   * @param {number} sessionId - 会话ID
   * @returns {Promise<Object>} 重置后的状态
   */
  async resetState(sessionId) {
    const resetState = {
      phase: this.states.COLLECTING_INFO,
      pendingFields: [...this.requiredFields],
      collectedFields: [],
      recommendation_count: 0,
      last_updated: new Date().toISOString(),
    };

    await database.updateSessionContext(sessionId, resetState);
    logger.info("Conversation state reset", { sessionId });
    return resetState;
  }
}

module.exports = new ConversationStateManager();
