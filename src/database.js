/**
 * Katrina-O3 数据库连接和查询模块
 * 使用 Supabase 作为数据库服务
 */

const { createClient } = require('@supabase/supabase-js');
const config = require('./config');
const logger = require('./logger');

class Database {
  constructor() {
    this.supabase = null;
    this.isConnected = false;
  }

  /**
   * 初始化数据库连接
   */
  async initialize() {
    try {
      this.supabase = createClient(
        config.database.supabaseUrl,
        config.database.supabaseAnonKey
      );

      // 测试连接
      const { data, error } = await this.supabase
        .from('users')
        .select('count')
        .limit(1);

      if (error) {
        throw error;
      }

      this.isConnected = true;
      logger.info('✅ Database connection established');
      return true;
    } catch (error) {
      logger.error('❌ Database connection failed:', error);
      this.isConnected = false;
      throw error;
    }
  }

  /**
   * 获取会话信息
   */
  async getSession(sessionUuid) {
    try {
      const { data, error } = await this.supabase
        .from('chat_sessions')
        .select('*')
        .eq('session_uuid', sessionUuid)
        .single();

      if (error && error.code !== 'PGRST116') { // PGRST116 = no rows returned
        throw error;
      }

      return data;
    } catch (error) {
      logger.error('Error getting session:', error);
      throw error;
    }
  }

  /**
   * 创建新会话
   */
  async createSession(sessionUuid, userId, entrySource = null, initialIntent = null) {
    try {
      const { data, error } = await this.supabase
        .from('chat_sessions')
        .insert({
          session_uuid: sessionUuid,
          user_id: userId,
          entry_source_url: entrySource,
          initial_intent: initialIntent,
          current_interaction_context: {},
          last_active_at: new Date().toISOString(),
          created_at: new Date().toISOString()
        })
        .select()
        .single();

      if (error) {
        throw error;
      }

      logger.info(`Session created: ${sessionUuid}`);
      return data;
    } catch (error) {
      logger.error('Error creating session:', error);
      throw error;
    }
  }

  /**
   * 获取对话历史
   */
  async getChatHistory(sessionId, limit = 10) {
    try {
      const { data, error } = await this.supabase
        .from('chat_messages')
        .select('*')
        .eq('session_id', sessionId)
        .order('timestamp', { ascending: false })
        .limit(limit);

      if (error) {
        throw error;
      }

      // 返回按时间正序排列的消息
      return data.reverse();
    } catch (error) {
      logger.error('Error getting chat history:', error);
      throw error;
    }
  }

  /**
   * 保存消息
   */
  async saveMessage(sessionId, messageType, content, metadata = {}) {
    try {
      const { data, error } = await this.supabase
        .from('chat_messages')
        .insert({
          session_id: sessionId,
          message_type: messageType, // 'user' or 'assistant'
          message_content: content,
          metadata_json: metadata,
          timestamp: new Date().toISOString()
        })
        .select()
        .single();

      if (error) {
        throw error;
      }

      return data;
    } catch (error) {
      logger.error('Error saving message:', error);
      throw error;
    }
  }

  /**
   * 获取候选人档案
   */
  async getCandidateProfile(userId) {
    try {
      const { data, error } = await this.supabase
        .from('candidate_profiles')
        .select('*')
        .eq('user_id', userId)
        .single();

      if (error && error.code !== 'PGRST116') {
        throw error;
      }

      return data;
    } catch (error) {
      logger.error('Error getting candidate profile:', error);
      throw error;
    }
  }

  /**
   * 更新候选人档案
   */
  async updateCandidateProfile(userId, profileData) {
    try {
      // 先尝试更新
      const { data: updateData, error: updateError } = await this.supabase
        .from('candidate_profiles')
        .update({
          ...profileData,
          last_updated_at: new Date().toISOString()
        })
        .eq('user_id', userId)
        .select()
        .single();

      if (updateError && updateError.code === 'PGRST116') {
        // 如果记录不存在，则创建新记录
        const { data: insertData, error: insertError } = await this.supabase
          .from('candidate_profiles')
          .insert({
            user_id: userId,
            ...profileData,
            created_at: new Date().toISOString(),
            last_updated_at: new Date().toISOString()
          })
          .select()
          .single();

        if (insertError) {
          throw insertError;
        }

        return insertData;
      } else if (updateError) {
        throw updateError;
      }

      return updateData;
    } catch (error) {
      logger.error('Error updating candidate profile:', error);
      throw error;
    }
  }

  /**
   * 搜索职位
   */
  async searchJobs(criteria) {
    try {
      let query = this.supabase
        .from('job_listings')
        .select(`
          *,
          companies (
            id,
            company_name,
            company_type,
            industry
          )
        `)
        .eq('is_active', true);

      // 应用筛选条件
      if (criteria.techDirectionId) {
        query = query.eq('primary_tech_direction_id', criteria.techDirectionId);
      }

      if (criteria.companyTypes && criteria.companyTypes.length > 0) {
        query = query.in('companies.company_type', criteria.companyTypes);
      }

      if (criteria.salaryMin) {
        query = query.gte('salary_max', criteria.salaryMin);
      }

      if (criteria.salaryMax) {
        query = query.lte('salary_min', criteria.salaryMax);
      }

      if (criteria.levelMin) {
        query = query.gte('job_standard_level_max', criteria.levelMin);
      }

      if (criteria.levelMax) {
        query = query.lte('job_standard_level_min', criteria.levelMax);
      }

      if (criteria.businessScenarioId) {
        query = query.eq('primary_business_scenario_id', criteria.businessScenarioId);
      }

      if (criteria.location) {
        query = query.ilike('location', `%${criteria.location}%`);
      }

      // 排序和限制
      query = query
        .order('priority_level', { ascending: false })
        .order('created_at', { ascending: false })
        .limit(criteria.limit || 200);

      const { data, error } = await query;

      if (error) {
        throw error;
      }

      return data;
    } catch (error) {
      logger.error('Error searching jobs:', error);
      throw error;
    }
  }

  /**
   * 获取技术方向信息
   */
  async getTechDirection(techId) {
    try {
      const { data, error } = await this.supabase
        .from('tech_tree')
        .select('*')
        .eq('id', techId)
        .single();

      if (error) {
        throw error;
      }

      return data;
    } catch (error) {
      logger.error('Error getting tech direction:', error);
      throw error;
    }
  }

  /**
   * 搜索技术方向（模糊匹配）
   */
  async searchTechDirections(keyword) {
    try {
      const { data, error } = await this.supabase
        .from('tech_tree')
        .select('*')
        .or(`tech_name.ilike.%${keyword}%,keywords.ilike.%${keyword}%`)
        .order('level', { ascending: true })
        .limit(10);

      if (error) {
        throw error;
      }

      return data;
    } catch (error) {
      logger.error('Error searching tech directions:', error);
      throw error;
    }
  }

  /**
   * 健康检查
   */
  async healthCheck() {
    try {
      const { data, error } = await this.supabase
        .from('users')
        .select('count')
        .limit(1);

      return { 
        status: error ? 'error' : 'ok', 
        error: error?.message,
        connected: this.isConnected 
      };
    } catch (error) {
      return { 
        status: 'error', 
        error: error.message,
        connected: false 
      };
    }
  }
}

// 创建单例实例
const database = new Database();

module.exports = database;
