/**
 * Unit tests for RecommendationEngine's internal strategy & formatting logic
 */

const recEngine = require('../recommendation');

describe('RecommendationEngine – strategy determination', () => {
  test('All four company types present -> default ABCD strategy', () => {
    const jobsByType = {
      '头部大厂': [{}],
      '国企': [{}],
      '中型公司': [{}],
      '创业型公司': [{}]
    };
    const strategy = recEngine._determineRecommendationStrategy(jobsByType);
    expect(strategy).toEqual(['头部大厂', '国企', '中型公司', '创业型公司']);
  });

  test('Missing "头部大厂" falls back to 国企-中型-创业-国企', () => {
    const jobsByType = {
      '国企': [{}],
      '中型公司': [{}],
      '创业型公司': [{}]
    };
    const strategy = recEngine._determineRecommendationStrategy(jobsByType);
    expect(strategy).toEqual(['国企', '中型公司', '创业型公司', '国企']);
  });

  test('Missing "头部大厂" & "国企" returns 中型-中型-中型-创业', () => {
    const jobsByType = {
      '中型公司': [{}],
      '创业型公司': [{}]
    };
    const strategy = recEngine._determineRecommendationStrategy(jobsByType);
    expect(strategy).toEqual(['中型公司', '中型公司', '中型公司', '创业型公司']);
  });

  test('Only 创业型公司 available returns all 创业型公司', () => {
    const jobsByType = {
      '创业型公司': [{}, {}]
    };
    const strategy = recEngine._determineRecommendationStrategy(jobsByType);
    expect(strategy).toEqual(['创业型公司', '创业型公司', '创业型公司', '创业型公司']);
  });
});

describe('RecommendationEngine – salary formatting helper', () => {
  test('No salary provided -> 面议', () => {
    expect(recEngine._formatSalary(null, null)).toBe('面议');
  });

  test('Both min & max -> return 区间', () => {
    expect(recEngine._formatSalary(30000, 50000)).toBe('3万-5万');
  });

  test('Only min -> return min+', () => {
    expect(recEngine._formatSalary(15000, null)).toBe('1万+');
  });

  test('Only max -> return 最高x', () => {
    expect(recEngine._formatSalary(null, 18000)).toBe('最高2万');
  });
});