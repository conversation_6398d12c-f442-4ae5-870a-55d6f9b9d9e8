/**
 * 信息收集系统测试
 * 测试新的6大信息收集逻辑、状态管理和追问机制
 */

// Mock 设置
jest.mock("../database");
jest.mock("../llm-client");

const conversationState = require("../conversation-state");
const followUpEngine = require("../follow-up-engine");
const infoExtractor = require("../info-extractor");
const database = require("../database");

describe("信息收集系统测试", () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe("对话状态管理", () => {
    test("应该正确初始化对话状态", async () => {
      // Mock 数据
      database.getSessionById.mockResolvedValue({
        id: 1,
        current_interaction_context: {}
      });

      const state = await conversationState.getCurrentState(1);

      expect(state.phase).toBe("collecting_info");
      expect(state.pendingFields).toHaveLength(6);
      expect(state.pendingFields).toContain("current_company");
      expect(state.pendingFields).toContain("tech_direction");
      expect(state.collectedFields).toHaveLength(0);
    });

    test("应该正确检测第一次推荐触发条件", () => {
      // 条件A: 公司 + 技术方向 + 职级
      const collectedA = ["current_company", "tech_direction", "level_or_grade"];
      expect(conversationState.canTriggerFirstRecommend(collectedA)).toBe(true);

      // 条件B: 公司 + 技术方向 + 期望薪酬
      const collectedB = ["current_company", "tech_direction", "expected_compensation"];
      expect(conversationState.canTriggerFirstRecommend(collectedB)).toBe(true);

      // 不满足条件
      const collectedIncomplete = ["current_company", "tech_direction"];
      expect(conversationState.canTriggerFirstRecommend(collectedIncomplete)).toBe(false);
    });

    test("应该正确检测第二次推荐触发条件", () => {
      // 已完成第一次推荐，补齐业务场景和城市
      const collected = [
        "current_company", "tech_direction", "level_or_grade",
        "business_scenario", "current_city_or_expected_city"
      ];
      expect(conversationState.canTriggerSecondRecommend(collected, 1)).toBe(true);

      // 未完成第一次推荐
      expect(conversationState.canTriggerSecondRecommend(collected, 0)).toBe(false);

      // 缺少必要字段
      const incompleteCollected = ["current_company", "tech_direction", "level_or_grade"];
      expect(conversationState.canTriggerSecondRecommend(incompleteCollected, 1)).toBe(false);
    });

    test("应该正确处理字段收集完成后的状态转移", async () => {
      // Mock 当前状态
      database.getSessionById.mockResolvedValue({
        id: 1,
        current_interaction_context: {
          phase: "collecting_info",
          collected_fields: ["current_company"],
          pending_fields: ["tech_direction", "level_or_grade", "expected_compensation", "current_city_or_expected_city", "business_scenario"],
          recommendation_count: 0
        }
      });

      database.updateSessionContext.mockResolvedValue({});

      // 收集技术方向和职级
      const result = await conversationState.processFieldCollection(1, ["tech_direction", "level_or_grade"]);

      expect(result.shouldRecommend).toBe(true);
      expect(result.recommendationTrigger).toBe("first");
      expect(result.state.phase).toBe("first_recommend");
    });
  });

  describe("追问逻辑引擎", () => {
    test("应该生成正确的追问话术", async () => {
      database.getCandidateProfile.mockResolvedValue({
        follow_up_counts: { current_company: 0 }
      });
      database.updateCandidateFollowUpCounts.mockResolvedValue({});

      const result = await followUpEngine.generateFollowUpQuestion(1, "current_company");

      expect(result.question).toContain("您目前在哪家公司工作呢？");
      expect(result.followUpCount).toBe(1);
      expect(result.isLastAttempt).toBe(false);
    });

    test("应该在达到最大追问次数时停止追问", async () => {
      database.getCandidateProfile.mockResolvedValue({
        follow_up_counts: { current_company: 3 }
      });

      const result = await followUpEngine.generateFollowUpQuestion(1, "current_company");

      expect(result).toBeNull();
    });

    test("应该正确检测候选人拒绝回答", () => {
      const refusalResponse = "这个不方便说";
      expect(followUpEngine.handleRefusal(refusalResponse)).toBe(true);

      const normalResponse = "我在阿里巴巴工作";
      expect(followUpEngine.handleRefusal(normalResponse)).toBe(false);
    });

    test("应该正确分析回复是否包含目标信息", () => {
      const companyResponse = "我在腾讯科技有限公司工作";
      expect(followUpEngine.analyzeResponse(companyResponse, "current_company")).toBe(true);

      const techResponse = "我主要做NLP相关的工作";
      expect(followUpEngine.analyzeResponse(techResponse, "tech_direction")).toBe(true);

      const irrelevantResponse = "今天天气不错";
      expect(followUpEngine.analyzeResponse(irrelevantResponse, "current_company")).toBe(false);
    });
  });

  describe("信息提取器", () => {
    test("应该正确检测代推荐角色", async () => {
      const agentMessage = "我想为我朋友推荐一些职位";
      const extractedData = { tech_direction: "NLP" };

      const enriched = await infoExtractor._enrichExtractedData(extractedData, agentMessage);

      expect(enriched.candidate_role).toBe("agent");
    });

    test("应该从消息中智能提取关键词", () => {
      const message = "我在北京做NLP算法，主要应用在金融领域";
      const keywords = infoExtractor._extractKeywordsFromMessage(message);

      expect(keywords.tech).toBe("nlp");
      expect(keywords.city).toBe("北京");
      expect(keywords.business).toBe("金融");
    });

    test("应该正确检测信息冲突", async () => {
      const newData = { current_company: "字节跳动" };
      const currentProfile = { current_company_name_raw: "阿里巴巴" };

      const conflictCheck = await infoExtractor._detectConflicts(newData, currentProfile);

      expect(conflictCheck.hasConflicts).toBe(true);
      expect(conflictCheck.conflicts).toHaveLength(1);
      expect(conflictCheck.conflicts[0].field).toBe("current_company");
      expect(conflictCheck.conflicts[0].severity).toBe("medium");
    });

    test("应该正确识别新收集的字段", () => {
      const updateData = {
        current_company_name_raw: "腾讯",
        candidate_tech_direction_raw: "CV",
        expected_compensation_min: 300000
      };

      const newFields = infoExtractor._identifyNewlyCollectedFields(updateData);

      expect(newFields).toContain("current_company");
      expect(newFields).toContain("tech_direction");
      expect(newFields).toContain("expected_compensation");
      expect(newFields).toHaveLength(3);
    });

    test("应该正确处理完整的信息提取流程", async () => {
      // Mock 数据
      database.getCandidateProfile.mockResolvedValue({
        id: 1,
        user_id: 1,
        current_company_name_raw: null
      });

      database.updateCandidateProfile.mockResolvedValue({
        id: 1,
        user_id: 1,
        current_company_name_raw: "阿里巴巴",
        profile_completeness_score: 60
      });

      database.getSessionById.mockResolvedValue({
        id: 1,
        current_interaction_context: {
          phase: "collecting_info",
          collected_fields: [],
          pending_fields: ["current_company", "tech_direction", "level_or_grade", "expected_compensation", "current_city_or_expected_city", "business_scenario"],
          recommendation_count: 0
        }
      });

      database.updateSessionContext.mockResolvedValue({});

      const extractedData = { current_company: "阿里巴巴" };
      const userMessage = "我在阿里巴巴工作";

      const result = await infoExtractor.processExtractedInfo(1, 1, extractedData, userMessage);

      expect(result.success).toBe(true);
      expect(result.newlyCollectedFields).toContain("current_company");
      expect(result.profile.current_company_name_raw).toBe("阿里巴巴");
    });
  });

  describe("集成测试", () => {
    test("应该完整处理6大信息收集流程", async () => {
      // 模拟完整的信息收集流程
      const sessionId = 1;
      const userId = 1;

      // 初始状态
      database.getSessionById.mockResolvedValue({
        id: sessionId,
        current_interaction_context: {}
      });

      database.updateSessionContext.mockResolvedValue({});
      database.getCandidateProfile.mockResolvedValue({ id: 1, user_id: userId });
      database.updateCandidateProfile.mockResolvedValue({});

      // 第一步：收集公司信息
      let result = await conversationState.processFieldCollection(sessionId, ["current_company"]);
      expect(result.shouldRecommend).toBe(false);

      // 第二步：收集技术方向
      result = await conversationState.processFieldCollection(sessionId, ["tech_direction"]);
      expect(result.shouldRecommend).toBe(false);

      // 第三步：收集职级 - 应该触发第一次推荐
      result = await conversationState.processFieldCollection(sessionId, ["level_or_grade"]);
      expect(result.shouldRecommend).toBe(true);
      expect(result.recommendationTrigger).toBe("first");

      // 标记第一次推荐完成
      await conversationState.markRecommendationCompleted(sessionId, "first");

      // 第四步：收集业务场景和城市 - 应该触发第二次推荐
      result = await conversationState.processFieldCollection(sessionId, ["business_scenario", "current_city_or_expected_city"]);
      expect(result.shouldRecommend).toBe(true);
      expect(result.recommendationTrigger).toBe("second");
    });
  });
});
