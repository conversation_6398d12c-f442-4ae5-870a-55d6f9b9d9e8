/**
 * Katrina-O3 集成测试
 * 端到端测试用例
 */

const request = require("supertest");
const server = require("../index");
const database = require("../database");
const llmClient = require("../llm-client");
const Utils = require("../utils");

describe("Katrina-O3 Integration Tests", () => {
  let app;
  let testSessionId;

  beforeAll(async () => {
    // 初始化服务器
    await server.initialize();
    app = server.app;

    // 生成测试会话 ID
    testSessionId = Utils.generateSessionId();

    console.log("🧪 Integration tests initialized");
  });

  afterAll(async () => {
    // 清理测试数据
    if (server.server) {
      server.server.close();
    }
    console.log("🧪 Integration tests completed");
  });

  describe("Health Check", () => {
    test("GET /health should return system status", async () => {
      const response = await request(app)
        .get("/health")
        .expect("Content-Type", /json/);

      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty("status");
      expect(response.body).toHaveProperty("timestamp");
      expect(response.body).toHaveProperty("services");
      expect(response.body.services).toHaveProperty("database");
      expect(response.body.services).toHaveProperty("llm");
    });
  });

  describe("Chat API", () => {
    test("POST /api/chat should handle first message", async () => {
      const response = await request(app)
        .post("/api/chat")
        .send({
          message: "你好",
          sessionId: testSessionId,
        })
        .expect("Content-Type", /json/)
        .expect(200);

      expect(response.body).toHaveProperty("success", true);
      expect(response.body).toHaveProperty("data");
      expect(response.body.data).toHaveProperty("reply");
      expect(response.body.data).toHaveProperty("conversationState");

      // 验证欢迎消息
      expect(response.body.data.reply).toContain("Katrina");
      expect(response.body.data.conversationState.phase).toBe("collecting");
    });

    test("POST /api/chat should handle company information", async () => {
      const response = await request(app)
        .post("/api/chat")
        .send({
          message: "我在阿里巴巴工作",
          sessionId: testSessionId,
        })
        .expect("Content-Type", /json/)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.reply).toBeTruthy();

      // 应该询问技术方向
      expect(response.body.data.reply.toLowerCase()).toMatch(
        /技术|方向|nlp|cv|算法/
      );
    });

    test("POST /api/chat should handle tech direction", async () => {
      const response = await request(app)
        .post("/api/chat")
        .send({
          message: "我做NLP算法",
          sessionId: testSessionId,
        })
        .expect("Content-Type", /json/)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.reply).toBeTruthy();

      // 应该询问职级或薪资
      expect(response.body.data.reply.toLowerCase()).toMatch(
        /职级|级别|薪资|薪酬/
      );
    });

    test("POST /api/chat should handle level information", async () => {
      const response = await request(app)
        .post("/api/chat")
        .send({
          message: "我是P7",
          sessionId: testSessionId,
        })
        .expect("Content-Type", /json/)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.reply).toBeTruthy();

      // 可能触发第一次推荐或继续收集信息
      const reply = response.body.data.reply.toLowerCase();
      const hasRecommendation =
        reply.includes("推荐") || reply.includes("职位");
      const askingMoreInfo =
        reply.includes("薪资") ||
        reply.includes("城市") ||
        reply.includes("业务");

      expect(hasRecommendation || askingMoreInfo).toBe(true);
    });

    test("POST /api/chat should handle invalid requests", async () => {
      // 测试空消息
      await request(app)
        .post("/api/chat")
        .send({
          message: "",
          sessionId: testSessionId,
        })
        .expect(400);

      // 测试缺少 sessionId
      await request(app)
        .post("/api/chat")
        .send({
          message: "测试消息",
        })
        .expect(400);
    });

    test("POST /api/chat should handle long messages", async () => {
      const longMessage = "A".repeat(1000);

      const response = await request(app)
        .post("/api/chat")
        .send({
          message: longMessage,
          sessionId: testSessionId,
        })
        .expect("Content-Type", /json/)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.reply).toBeTruthy();
    });
  });

  describe("Session Management", () => {
    test("GET /api/session/:sessionId should return session state", async () => {
      const response = await request(app)
        .get(`/api/session/${testSessionId}`)
        .expect("Content-Type", /json/)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data).toHaveProperty("sessionId");
      expect(response.body.data).toHaveProperty("userId");
      expect(response.body.data).toHaveProperty("phase");
      expect(response.body.data).toHaveProperty("profileCompleteness");
    });

    test("GET /api/session/:sessionId should handle non-existent session", async () => {
      const nonExistentSessionId = Utils.generateSessionId();

      await request(app)
        .get(`/api/session/${nonExistentSessionId}`)
        .expect(404);
    });

    test("POST /api/session/:sessionId/reset should reset session", async () => {
      const response = await request(app)
        .post(`/api/session/${testSessionId}/reset`)
        .expect("Content-Type", /json/)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.message).toContain("重置");
    });
  });

  describe("LLM Stats", () => {
    test("GET /api/stats/llm should return LLM statistics", async () => {
      const response = await request(app)
        .get("/api/stats/llm")
        .expect("Content-Type", /json/)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data).toHaveProperty("provider");
      expect(response.body.data).toHaveProperty("requestCount");
      expect(response.body.data).toHaveProperty("totalCost");
    });
  });

  describe("Error Handling", () => {
    test("should handle 404 for unknown routes", async () => {
      await request(app).get("/api/unknown-route").expect(404);
    });

    test("should handle malformed JSON", async () => {
      await request(app)
        .post("/api/chat")
        .set("Content-Type", "application/json")
        .send("invalid json")
        .expect(400);
    });
  });

  describe("Database Integration", () => {
    test("database should be connected", async () => {
      const health = await database.healthCheck();
      expect(health.status).toBe("ok");
      expect(health.connected).toBe(true);
    });

    test("should be able to create and retrieve session", async () => {
      const newSessionId = Utils.generateSessionId();

      // 创建会话应该通过 API 调用自动完成（不指定 userId，让系统自动创建）
      const response = await request(app)
        .post("/api/chat")
        .send({
          message: "测试会话创建",
          sessionId: newSessionId,
        })
        .expect(200);

      expect(response.body.success).toBe(true);

      // 验证会话状态
      const sessionResponse = await request(app)
        .get(`/api/session/${newSessionId}`)
        .expect(200);

      expect(sessionResponse.body.data.sessionId).toBeTruthy();
    });
  });

  describe("LLM Integration", () => {
    test("LLM client should be healthy", async () => {
      const health = await llmClient.healthCheck();
      expect(["healthy", "unhealthy"]).toContain(health.status);
      expect(health.provider).toBeTruthy();
    });

    test("LLM should generate responses", async () => {
      try {
        const response = await llmClient.generate(
          "Hello, this is a test message.",
          {
            maxTokens: 50,
          }
        );

        expect(response).toHaveProperty("content");
        expect(response).toHaveProperty("model");
        expect(response).toHaveProperty("usage");
        expect(response.content).toBeTruthy();
      } catch (error) {
        // LLM 可能不可用，记录但不失败测试
        console.warn("LLM test skipped due to:", error.message);
      }
    });
  });

  describe("Utils Functions", () => {
    test("should generate valid UUIDs", () => {
      const uuid1 = Utils.generateUUID();
      const uuid2 = Utils.generateUUID();

      expect(uuid1).toBeTruthy();
      expect(uuid2).toBeTruthy();
      expect(uuid1).not.toBe(uuid2);
      expect(uuid1).toMatch(
        /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i
      );
    });

    test("should extract salary ranges correctly", () => {
      const testCases = [
        { input: "30k-50k", expected: { min: 30000, max: 50000 } },
        { input: "年薪30万-50万", expected: { min: 300000, max: 500000 } },
        { input: "月薪20k", expected: { min: 192000, max: 288000 } }, // 20k * 12 * 0.8-1.2
      ];

      testCases.forEach(({ input, expected }) => {
        const result = Utils.extractSalaryRange(input);
        expect(result).toBeTruthy();
        expect(result.min).toBeCloseTo(expected.min, -3); // 允许千位误差
        expect(result.max).toBeCloseTo(expected.max, -3);
      });
    });

    test("should standardize city names", () => {
      const testCases = [
        { input: "北京", expected: "北京" },
        { input: "帝都", expected: "北京" },
        { input: "上海", expected: "上海" },
        { input: "魔都", expected: "上海" },
      ];

      testCases.forEach(({ input, expected }) => {
        const result = Utils.standardizeCity(input);
        expect(result).toBe(expected);
      });
    });
  });
});
