/**
 * Katrina-O3 信息提取模块
 * 解析 LLM 返回的 JSON 并更新候选人档案
 * 支持智能关键词识别、角色判定、增量更新和冲突检测
 */

const database = require("./database");
const logger = require("./logger");
const Utils = require("./utils");
const conversationState = require("./conversation-state");
const followUpEngine = require("./follow-up-engine");

class InfoExtractor {
  constructor() {
    this.techMappings = this._initTechMappings();
    this.companyMappings = this._initCompanyMappings();
    this.levelMappings = this._initLevelMappings();
    this.cityMappings = this._initCityMappings();
    this.businessScenarioMappings = this._initBusinessScenarioMappings();
  }

  /**
   * 处理提取的信息 - 重构版本
   * 支持智能识别、角色判定、增量更新和冲突检测
   */
  async processExtractedInfo(userId, sessionId, extractedData, userMessage) {
    try {
      if (!extractedData || typeof extractedData !== "object") {
        logger.warn("Invalid extracted data format", { userId, extractedData });
        return { success: false, reason: "invalid_format" };
      }

      // 获取当前档案
      const currentProfile = await database.getCandidateProfile(userId);

      // 智能识别和角色判定
      const enrichedData = await this._enrichExtractedData(
        extractedData,
        userMessage
      );

      // 检测信息冲突
      const conflictCheck = await this._detectConflicts(
        enrichedData,
        currentProfile
      );
      if (conflictCheck.hasConflicts) {
        logger.warn("Information conflicts detected", {
          userId,
          conflicts: conflictCheck.conflicts,
        });
      }

      // 构建更新数据
      const updateData = await this._buildUpdateData(
        enrichedData,
        currentProfile
      );

      if (Object.keys(updateData).length === 0) {
        logger.info("No new information to update", { userId });
        return {
          success: true,
          profile: currentProfile,
          newlyCollectedFields: [],
        };
      }

      // 更新档案
      const updatedProfile = await database.updateCandidateProfile(
        userId,
        updateData
      );

      // 识别新收集的字段
      const newlyCollectedFields =
        this._identifyNewlyCollectedFields(updateData);

      // 更新对话状态
      const stateUpdate = await conversationState.processFieldCollection(
        sessionId,
        newlyCollectedFields
      );

      logger.logBusinessEvent("profile_updated", {
        userId,
        sessionId,
        updatedFields: Object.keys(updateData),
        newlyCollectedFields,
        completeness: updatedProfile.profile_completeness_score,
        conversationPhase: stateUpdate.state.phase,
      });

      return {
        success: true,
        profile: updatedProfile,
        newlyCollectedFields,
        conversationState: stateUpdate.state,
        shouldRecommend: stateUpdate.shouldRecommend,
        recommendationTrigger: stateUpdate.recommendationTrigger,
        conflicts: conflictCheck.conflicts,
      };
    } catch (error) {
      logger.logError(error, { userId, sessionId, extractedData });
      throw error;
    }
  }

  /**
   * 构建更新数据
   */
  async _buildUpdateData(extractedData, currentProfile) {
    const updateData = {};

    // 处理各个字段
    await this._processCompanyInfo(extractedData, updateData);
    await this._processTechDirection(extractedData, updateData);
    await this._processLevel(extractedData, updateData);
    await this._processCompensation(extractedData, updateData);
    await this._processLocation(extractedData, updateData);
    await this._processBusinessScenario(extractedData, updateData);

    // 计算完整度评分
    const mergedProfile = { ...currentProfile, ...updateData };
    updateData.profile_completeness_score =
      Utils.calculateProfileCompleteness(mergedProfile);

    return updateData;
  }

  /**
   * 处理公司信息
   */
  async _processCompanyInfo(extractedData, updateData) {
    const companyInfo = extractedData.current_company || extractedData.company;
    if (!companyInfo) return;

    const companyName = this._normalizeCompanyName(companyInfo);
    updateData.current_company_name_raw = companyName;

    // 尝试匹配数据库中的公司
    try {
      const matchedCompany = await this._findCompanyInDatabase(companyName);
      if (matchedCompany) {
        updateData.current_company_id = matchedCompany.id;
      }
    } catch (error) {
      logger.warn("Failed to match company in database", {
        companyName,
        error: error.message,
      });
    }
  }

  /**
   * 处理技术方向
   */
  async _processTechDirection(extractedData, updateData) {
    const techInfo = extractedData.tech_direction || extractedData.technology;
    if (!techInfo) return;

    updateData.candidate_tech_direction_raw = techInfo;

    // 尝试匹配技术方向 ID
    try {
      const techDirections = await database.searchTechDirections(techInfo);
      if (techDirections && techDirections.length > 0) {
        // 选择最匹配的技术方向
        const bestMatch = this._findBestTechMatch(techInfo, techDirections);
        if (bestMatch) {
          updateData.primary_tech_direction_id = bestMatch.id;
        }
      }
    } catch (error) {
      logger.warn("Failed to match tech direction", {
        techInfo,
        error: error.message,
      });
    }
  }

  /**
   * 处理职级信息
   */
  async _processLevel(extractedData, updateData) {
    const levelInfo =
      extractedData.level_or_grade ||
      extractedData.level ||
      extractedData.grade;
    if (!levelInfo) return;

    updateData.candidate_level_raw = levelInfo;

    // 转换为标准职级
    const standardLevel = this._convertToStandardLevel(levelInfo);
    if (standardLevel) {
      updateData.candidate_standard_level_min = standardLevel.min;
      updateData.candidate_standard_level_max = standardLevel.max;
    }
  }

  /**
   * 处理薪酬信息
   */
  async _processCompensation(extractedData, updateData) {
    const compensationInfo =
      extractedData.expected_compensation ||
      extractedData.salary ||
      extractedData.compensation;
    if (!compensationInfo) return;

    updateData.expected_compensation_raw = compensationInfo;

    // 提取薪资范围
    const salaryRange = Utils.extractSalaryRange(compensationInfo);
    if (salaryRange) {
      updateData.expected_compensation_min = salaryRange.min;
      updateData.expected_compensation_max = salaryRange.max;
    }
  }

  /**
   * 处理地理位置
   */
  async _processLocation(extractedData, updateData) {
    const locationInfo =
      extractedData.current_city_or_expected_city ||
      extractedData.location ||
      extractedData.city;
    if (!locationInfo) return;

    updateData.desired_location_raw = locationInfo;

    // 标准化城市名称
    const standardCity = Utils.standardizeCity(locationInfo);
    updateData.desired_location_standard = standardCity;
  }

  /**
   * 处理业务场景
   */
  async _processBusinessScenario(extractedData, updateData) {
    const scenarioInfo =
      extractedData.business_scenario ||
      extractedData.business ||
      extractedData.scenario;
    if (!scenarioInfo) return;

    updateData.candidate_business_scenario_raw = scenarioInfo;

    // 尝试匹配业务场景 ID
    try {
      const scenarios = await this._searchBusinessScenarios(scenarioInfo);
      if (scenarios && scenarios.length > 0) {
        const bestMatch = this._findBestScenarioMatch(scenarioInfo, scenarios);
        if (bestMatch) {
          updateData.primary_business_scenario_id = bestMatch.id;
        }
      }
    } catch (error) {
      logger.warn("Failed to match business scenario", {
        scenarioInfo,
        error: error.message,
      });
    }
  }

  /**
   * 标准化公司名称
   */
  _normalizeCompanyName(companyName) {
    if (!companyName) return "";

    return companyName
      .replace(/有限公司|股份有限公司|科技有限公司/g, "")
      .replace(/\(.*?\)/g, "") // 移除括号内容
      .replace(/（.*?）/g, "") // 移除中文括号内容
      .trim();
  }

  /**
   * 在数据库中查找公司
   */
  async _findCompanyInDatabase(companyName) {
    try {
      // 这里应该调用数据库搜索公司的方法
      // 由于当前 database.js 没有这个方法，我们先返回 null
      // TODO: 实现公司搜索功能
      return null;
    } catch (error) {
      logger.warn("Company search failed", {
        companyName,
        error: error.message,
      });
      return null;
    }
  }

  /**
   * 找到最佳技术方向匹配
   */
  _findBestTechMatch(inputTech, techDirections) {
    if (!techDirections || techDirections.length === 0) return null;

    let bestMatch = null;
    let highestScore = 0;

    for (const tech of techDirections) {
      // 计算匹配分数
      let score = 0;

      // 名称完全匹配
      if (tech.tech_name.toLowerCase() === inputTech.toLowerCase()) {
        score += 100;
      }

      // 名称包含匹配
      if (
        tech.tech_name.toLowerCase().includes(inputTech.toLowerCase()) ||
        inputTech.toLowerCase().includes(tech.tech_name.toLowerCase())
      ) {
        score += 50;
      }

      // 关键词匹配
      if (tech.keywords) {
        const keywords = tech.keywords.toLowerCase().split(",");
        if (
          keywords.some(
            (keyword) =>
              inputTech.toLowerCase().includes(keyword.trim()) ||
              keyword.trim().includes(inputTech.toLowerCase())
          )
        ) {
          score += 30;
        }
      }

      // 优先选择更高层级的技术方向
      if (tech.level === 1) score += 10;
      else if (tech.level === 2) score += 5;

      if (score > highestScore) {
        highestScore = score;
        bestMatch = tech;
      }
    }

    return highestScore > 20 ? bestMatch : null; // 设置最低匹配阈值
  }

  /**
   * 转换为标准职级
   */
  _convertToStandardLevel(levelText) {
    const levelMappings = {
      // 阿里系
      P4: { min: 4, max: 4 },
      P5: { min: 5, max: 5 },
      P6: { min: 6, max: 6 },
      P7: { min: 7, max: 7 },
      P8: { min: 8, max: 8 },
      P9: { min: 9, max: 9 },
      P10: { min: 10, max: 10 },

      // 通用职级
      初级工程师: { min: 4, max: 5 },
      工程师: { min: 5, max: 6 },
      高级工程师: { min: 6, max: 7 },
      资深工程师: { min: 7, max: 8 },
      专家工程师: { min: 7, max: 8 },
      资深专家: { min: 8, max: 9 },
      首席专家: { min: 9, max: 10 },
      技术总监: { min: 10, max: 11 },
    };

    const normalizedLevel = levelText.trim();

    // 直接匹配
    if (levelMappings[normalizedLevel]) {
      return levelMappings[normalizedLevel];
    }

    // 模糊匹配
    for (const [key, value] of Object.entries(levelMappings)) {
      if (normalizedLevel.includes(key) || key.includes(normalizedLevel)) {
        return value;
      }
    }

    return null;
  }

  /**
   * 搜索业务场景
   */
  async _searchBusinessScenarios(scenarioText) {
    try {
      // TODO: 实现业务场景搜索
      // 这里应该调用数据库搜索业务场景的方法
      return [];
    } catch (error) {
      logger.warn("Business scenario search failed", {
        scenarioText,
        error: error.message,
      });
      return [];
    }
  }

  /**
   * 找到最佳业务场景匹配
   */
  _findBestScenarioMatch(inputScenario, scenarios) {
    // 类似于技术方向匹配的逻辑
    if (!scenarios || scenarios.length === 0) return null;

    let bestMatch = null;
    let highestScore = 0;

    for (const scenario of scenarios) {
      let score = 0;

      if (
        scenario.scenario_name.toLowerCase() === inputScenario.toLowerCase()
      ) {
        score += 100;
      }

      if (
        scenario.scenario_name
          .toLowerCase()
          .includes(inputScenario.toLowerCase()) ||
        inputScenario
          .toLowerCase()
          .includes(scenario.scenario_name.toLowerCase())
      ) {
        score += 50;
      }

      if (scenario.keywords) {
        const keywords = scenario.keywords.toLowerCase().split(",");
        if (
          keywords.some(
            (keyword) =>
              inputScenario.toLowerCase().includes(keyword.trim()) ||
              keyword.trim().includes(inputScenario.toLowerCase())
          )
        ) {
          score += 30;
        }
      }

      if (score > highestScore) {
        highestScore = score;
        bestMatch = scenario;
      }
    }

    return highestScore > 20 ? bestMatch : null;
  }

  /**
   * 初始化技术映射
   */
  _initTechMappings() {
    return {
      nlp: ["自然语言处理", "NLP", "文本处理"],
      cv: ["计算机视觉", "CV", "图像处理", "视觉"],
      推荐: ["推荐算法", "推荐系统"],
      搜索: ["搜索算法", "搜索引擎"],
      广告: ["广告算法", "广告投放"],
      多模态: ["多模态算法", "多模态学习"],
      大模型: ["LLM", "大语言模型", "GPT"],
      语音: ["语音识别", "语音合成", "ASR", "TTS"],
    };
  }

  /**
   * 初始化公司映射
   */
  _initCompanyMappings() {
    return {
      阿里: ["阿里巴巴", "阿里", "Alibaba"],
      腾讯: ["腾讯", "鹅厂", "Tencent"],
      字节: ["字节跳动", "字节", "ByteDance"],
      百度: ["百度", "Baidu"],
      美团: ["美团", "Meituan"],
    };
  }

  /**
   * 初始化职级映射
   */
  _initLevelMappings() {
    return {
      junior: { min: 4, max: 5 },
      senior: { min: 6, max: 7 },
      expert: { min: 7, max: 8 },
      principal: { min: 9, max: 10 },
    };
  }

  /**
   * 智能识别和数据增强
   */
  async _enrichExtractedData(extractedData, userMessage) {
    const enriched = { ...extractedData };

    // 角色判定：检测是否为代推荐
    if (this._detectAgentRole(userMessage)) {
      enriched.candidate_role = "agent";
    }

    // 智能关键词识别
    if (userMessage) {
      const keywords = this._extractKeywordsFromMessage(userMessage);

      // 补充缺失的技术方向
      if (!enriched.tech_direction && keywords.tech) {
        enriched.tech_direction = keywords.tech;
      }

      // 补充缺失的城市信息
      if (!enriched.current_city_or_expected_city && keywords.city) {
        enriched.current_city_or_expected_city = keywords.city;
      }

      // 补充缺失的业务场景
      if (!enriched.business_scenario && keywords.business) {
        enriched.business_scenario = keywords.business;
      }
    }

    return enriched;
  }

  /**
   * 检测代推荐角色
   */
  _detectAgentRole(message) {
    const agentKeywords = [
      "朋友",
      "同事",
      "同学",
      "他",
      "她",
      "帮忙",
      "代为",
      "推荐给",
      "介绍给",
      "替他",
      "替她",
      "为他",
      "为她",
    ];

    const lowerMessage = message.toLowerCase();
    return agentKeywords.some((keyword) => lowerMessage.includes(keyword));
  }

  /**
   * 从消息中提取关键词
   */
  _extractKeywordsFromMessage(message) {
    const keywords = { tech: null, city: null, business: null };
    const lowerMessage = message.toLowerCase();

    // 技术方向关键词
    const techKeywords = {
      nlp: ["nlp", "自然语言", "文本处理", "语言模型"],
      cv: ["cv", "计算机视觉", "图像", "视觉", "图像处理"],
      推荐算法: ["推荐", "推荐系统", "推荐算法", "recommendation"],
      机器学习: ["机器学习", "ml", "machine learning"],
      深度学习: ["深度学习", "dl", "deep learning"],
    };

    for (const [tech, words] of Object.entries(techKeywords)) {
      if (words.some((word) => lowerMessage.includes(word))) {
        keywords.tech = tech;
        break;
      }
    }

    // 城市关键词
    const cityKeywords = [
      "北京",
      "上海",
      "深圳",
      "杭州",
      "广州",
      "成都",
      "南京",
      "武汉",
      "西安",
      "苏州",
      "天津",
      "重庆",
    ];

    for (const city of cityKeywords) {
      if (lowerMessage.includes(city)) {
        keywords.city = city;
        break;
      }
    }

    // 业务场景关键词
    const businessKeywords = {
      金融: ["金融", "银行", "支付", "风控", "量化"],
      电商: ["电商", "购物", "零售", "商城", "淘宝", "京东"],
      教育: ["教育", "在线教育", "培训", "学习"],
      医疗: ["医疗", "健康", "医院", "诊断"],
      游戏: ["游戏", "娱乐", "手游"],
      社交: ["社交", "社区", "聊天", "直播"],
    };

    for (const [business, words] of Object.entries(businessKeywords)) {
      if (words.some((word) => lowerMessage.includes(word))) {
        keywords.business = business;
        break;
      }
    }

    return keywords;
  }

  /**
   * 检测信息冲突
   */
  async _detectConflicts(newData, currentProfile) {
    const conflicts = [];

    if (!currentProfile) {
      return { hasConflicts: false, conflicts: [] };
    }

    // 检查公司信息冲突
    if (newData.current_company && currentProfile.current_company_name_raw) {
      if (newData.current_company !== currentProfile.current_company_name_raw) {
        conflicts.push({
          field: "current_company",
          oldValue: currentProfile.current_company_name_raw,
          newValue: newData.current_company,
          severity: "medium",
        });
      }
    }

    // 检查技术方向冲突
    if (newData.tech_direction && currentProfile.candidate_tech_direction_raw) {
      if (
        newData.tech_direction !== currentProfile.candidate_tech_direction_raw
      ) {
        conflicts.push({
          field: "tech_direction",
          oldValue: currentProfile.candidate_tech_direction_raw,
          newValue: newData.tech_direction,
          severity: "high",
        });
      }
    }

    return {
      hasConflicts: conflicts.length > 0,
      conflicts,
    };
  }

  /**
   * 识别新收集的字段
   */
  _identifyNewlyCollectedFields(updateData) {
    const fieldMapping = {
      current_company_name_raw: "current_company",
      candidate_tech_direction_raw: "tech_direction",
      candidate_level_raw: "level_or_grade",
      expected_compensation_min: "expected_compensation",
      expected_compensation_max: "expected_compensation",
      current_city: "current_city_or_expected_city",
      expected_city: "current_city_or_expected_city",
      candidate_business_scenario_raw: "business_scenario",
    };

    const collectedFields = new Set();

    for (const dbField of Object.keys(updateData)) {
      const businessField = fieldMapping[dbField];
      if (businessField) {
        collectedFields.add(businessField);
      }
    }

    return Array.from(collectedFields);
  }

  /**
   * 初始化城市映射
   */
  _initCityMappings() {
    return {
      北京: ["北京", "beijing", "bj"],
      上海: ["上海", "shanghai", "sh"],
      深圳: ["深圳", "shenzhen", "sz"],
      杭州: ["杭州", "hangzhou", "hz"],
      广州: ["广州", "guangzhou", "gz"],
      成都: ["成都", "chengdu", "cd"],
      南京: ["南京", "nanjing", "nj"],
      武汉: ["武汉", "wuhan", "wh"],
      西安: ["西安", "xian", "xa"],
      苏州: ["苏州", "suzhou", "sz"],
      天津: ["天津", "tianjin", "tj"],
      重庆: ["重庆", "chongqing", "cq"],
    };
  }

  /**
   * 初始化业务场景映射
   */
  _initBusinessScenarioMappings() {
    return {
      金融科技: ["金融", "银行", "支付", "风控", "量化", "保险"],
      电商零售: ["电商", "购物", "零售", "商城", "淘宝", "京东"],
      在线教育: ["教育", "在线教育", "培训", "学习", "知识"],
      医疗健康: ["医疗", "健康", "医院", "诊断", "药物"],
      游戏娱乐: ["游戏", "娱乐", "手游", "直播"],
      社交媒体: ["社交", "社区", "聊天", "微信", "微博"],
      企业服务: ["企业", "办公", "saas", "b2b"],
      智能硬件: ["硬件", "物联网", "iot", "智能设备"],
      自动驾驶: ["自动驾驶", "无人驾驶", "车联网", "汽车"],
      搜索推荐: ["搜索", "推荐", "信息检索", "个性化"],
    };
  }
}

// 创建单例实例
const infoExtractor = new InfoExtractor();

module.exports = infoExtractor;
