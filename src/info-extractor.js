/**
 * Katrina-O3 信息提取模块
 * 解析 LLM 返回的 JSON 并更新候选人档案
 * 支持智能关键词识别、角色判定、增量更新和冲突检测
 */

const database = require("./database");
const logger = require("./logger");
const Utils = require("./utils");
// 移除已删除的模块引用
// const conversationState = require("./conversation/conversation-state");
// const followUpEngine = require("./conversation/follow-up-engine");

class InfoExtractor {
  constructor() {
    this.techMappings = this._initTechMappings();
    this.companyMappings = this._initCompanyMappings();
    this.levelMappings = this._initLevelMappings();
  }

  /**
   * 处理提取的信息 - 简化版本
   * 支持基础的信息提取和更新
   */
  async processExtractedInfo(userId, extractedData) {
    try {
      if (!extractedData || typeof extractedData !== "object") {
        logger.warn("Invalid extracted data format", { userId, extractedData });
        return { success: false, reason: "invalid_format" };
      }

      // 获取当前档案
      const currentProfile = await database.getCandidateProfile(userId);

      // 构建更新数据
      const updateData = await this._buildUpdateData(
        extractedData,
        currentProfile
      );

      if (Object.keys(updateData).length === 0) {
        logger.info("No new information to update", { userId });
        return currentProfile;
      }

      // 更新档案
      const updatedProfile = await database.updateCandidateProfile(
        userId,
        updateData
      );

      logger.logBusinessEvent("profile_updated", {
        userId,
        updatedFields: Object.keys(updateData),
        completeness: updatedProfile.profile_completeness_score,
      });

      return updatedProfile;
    } catch (error) {
      logger.logError(error, { userId, extractedData });
      throw error;
    }
  }

  /**
   * 构建更新数据
   */
  async _buildUpdateData(extractedData, currentProfile) {
    const updateData = {};

    // 处理各个字段
    await this._processCompanyInfo(extractedData, updateData);
    await this._processTechDirection(extractedData, updateData);
    await this._processLevel(extractedData, updateData);
    await this._processCompensation(extractedData, updateData);
    await this._processLocation(extractedData, updateData);
    await this._processBusinessScenario(extractedData, updateData);

    // 计算完整度评分
    const mergedProfile = { ...currentProfile, ...updateData };
    updateData.profile_completeness_score =
      Utils.calculateProfileCompleteness(mergedProfile);

    return updateData;
  }

  /**
   * 处理公司信息
   */
  async _processCompanyInfo(extractedData, updateData) {
    const companyInfo = extractedData.current_company || extractedData.company;
    if (!companyInfo) return;

    const companyName = this._normalizeCompanyName(companyInfo);
    updateData.current_company_name_raw = companyName;

    // 尝试匹配数据库中的公司
    try {
      const matchedCompany = await this._findCompanyInDatabase(companyName);
      if (matchedCompany) {
        updateData.current_company_id = matchedCompany.id;
      }
    } catch (error) {
      logger.warn("Failed to match company in database", {
        companyName,
        error: error.message,
      });
    }
  }

  /**
   * 处理技术方向
   */
  async _processTechDirection(extractedData, updateData) {
    const techInfo = extractedData.tech_direction || extractedData.technology;
    if (!techInfo) return;

    updateData.candidate_tech_direction_raw = techInfo;

    // 尝试匹配技术方向 ID
    try {
      const techDirections = await database.searchTechDirections(techInfo);
      if (techDirections && techDirections.length > 0) {
        // 选择最匹配的技术方向
        const bestMatch = this._findBestTechMatch(techInfo, techDirections);
        if (bestMatch) {
          updateData.primary_tech_direction_id = bestMatch.id;
        }
      }
    } catch (error) {
      logger.warn("Failed to match tech direction", {
        techInfo,
        error: error.message,
      });
    }
  }

  /**
   * 处理职级信息
   */
  async _processLevel(extractedData, updateData) {
    const levelInfo =
      extractedData.level_or_grade ||
      extractedData.level ||
      extractedData.grade;
    if (!levelInfo) return;

    updateData.candidate_level_raw = levelInfo;

    // 转换为标准职级
    const standardLevel = this._convertToStandardLevel(levelInfo);
    if (standardLevel) {
      updateData.candidate_standard_level_min = standardLevel.min;
      updateData.candidate_standard_level_max = standardLevel.max;
    }
  }

  /**
   * 处理薪酬信息
   */
  async _processCompensation(extractedData, updateData) {
    const compensationInfo =
      extractedData.expected_compensation ||
      extractedData.salary ||
      extractedData.compensation;
    if (!compensationInfo) return;

    updateData.expected_compensation_raw = compensationInfo;

    // 提取薪资范围
    const salaryRange = Utils.extractSalaryRange(compensationInfo);
    if (salaryRange) {
      updateData.expected_compensation_min = salaryRange.min;
      updateData.expected_compensation_max = salaryRange.max;
    }
  }

  /**
   * 处理地理位置
   */
  async _processLocation(extractedData, updateData) {
    const locationInfo =
      extractedData.current_city_or_expected_city ||
      extractedData.location ||
      extractedData.city;
    if (!locationInfo) return;

    updateData.desired_location_raw = locationInfo;

    // 标准化城市名称
    const standardCity = Utils.standardizeCity(locationInfo);
    updateData.desired_location_standard = standardCity;
  }

  /**
   * 处理业务场景
   */
  async _processBusinessScenario(extractedData, updateData) {
    const scenarioInfo =
      extractedData.business_scenario ||
      extractedData.business ||
      extractedData.scenario;
    if (!scenarioInfo) return;

    updateData.candidate_business_scenario_raw = scenarioInfo;

    // 尝试匹配业务场景 ID
    try {
      const scenarios = await this._searchBusinessScenarios(scenarioInfo);
      if (scenarios && scenarios.length > 0) {
        const bestMatch = this._findBestScenarioMatch(scenarioInfo, scenarios);
        if (bestMatch) {
          updateData.primary_business_scenario_id = bestMatch.id;
        }
      }
    } catch (error) {
      logger.warn("Failed to match business scenario", {
        scenarioInfo,
        error: error.message,
      });
    }
  }

  /**
   * 标准化公司名称
   */
  _normalizeCompanyName(companyName) {
    if (!companyName) return "";

    return companyName
      .replace(/有限公司|股份有限公司|科技有限公司/g, "")
      .replace(/\(.*?\)/g, "") // 移除括号内容
      .replace(/（.*?）/g, "") // 移除中文括号内容
      .trim();
  }

  /**
   * 在数据库中查找公司
   */
  async _findCompanyInDatabase(companyName) {
    try {
      // 这里应该调用数据库搜索公司的方法
      // 由于当前 database.js 没有这个方法，我们先返回 null
      // TODO: 实现公司搜索功能
      return null;
    } catch (error) {
      logger.warn("Company search failed", {
        companyName,
        error: error.message,
      });
      return null;
    }
  }

  /**
   * 找到最佳技术方向匹配
   */
  _findBestTechMatch(inputTech, techDirections) {
    if (!techDirections || techDirections.length === 0) return null;

    let bestMatch = null;
    let highestScore = 0;

    for (const tech of techDirections) {
      // 计算匹配分数
      let score = 0;

      // 名称完全匹配
      if (tech.tech_name.toLowerCase() === inputTech.toLowerCase()) {
        score += 100;
      }

      // 名称包含匹配
      if (
        tech.tech_name.toLowerCase().includes(inputTech.toLowerCase()) ||
        inputTech.toLowerCase().includes(tech.tech_name.toLowerCase())
      ) {
        score += 50;
      }

      // 关键词匹配
      if (tech.keywords) {
        const keywords = tech.keywords.toLowerCase().split(",");
        if (
          keywords.some(
            (keyword) =>
              inputTech.toLowerCase().includes(keyword.trim()) ||
              keyword.trim().includes(inputTech.toLowerCase())
          )
        ) {
          score += 30;
        }
      }

      // 优先选择更高层级的技术方向
      if (tech.level === 1) score += 10;
      else if (tech.level === 2) score += 5;

      if (score > highestScore) {
        highestScore = score;
        bestMatch = tech;
      }
    }

    return highestScore > 20 ? bestMatch : null; // 设置最低匹配阈值
  }

  /**
   * 转换为标准职级
   */
  _convertToStandardLevel(levelText) {
    const levelMappings = {
      // 阿里系
      P4: { min: 4, max: 4 },
      P5: { min: 5, max: 5 },
      P6: { min: 6, max: 6 },
      P7: { min: 7, max: 7 },
      P8: { min: 8, max: 8 },
      P9: { min: 9, max: 9 },
      P10: { min: 10, max: 10 },

      // 通用职级
      初级工程师: { min: 4, max: 5 },
      工程师: { min: 5, max: 6 },
      高级工程师: { min: 6, max: 7 },
      资深工程师: { min: 7, max: 8 },
      专家工程师: { min: 7, max: 8 },
      资深专家: { min: 8, max: 9 },
      首席专家: { min: 9, max: 10 },
      技术总监: { min: 10, max: 11 },
    };

    const normalizedLevel = levelText.trim();

    // 直接匹配
    if (levelMappings[normalizedLevel]) {
      return levelMappings[normalizedLevel];
    }

    // 模糊匹配
    for (const [key, value] of Object.entries(levelMappings)) {
      if (normalizedLevel.includes(key) || key.includes(normalizedLevel)) {
        return value;
      }
    }

    return null;
  }

  /**
   * 搜索业务场景
   */
  async _searchBusinessScenarios(scenarioText) {
    try {
      // TODO: 实现业务场景搜索
      // 这里应该调用数据库搜索业务场景的方法
      return [];
    } catch (error) {
      logger.warn("Business scenario search failed", {
        scenarioText,
        error: error.message,
      });
      return [];
    }
  }

  /**
   * 找到最佳业务场景匹配
   */
  _findBestScenarioMatch(inputScenario, scenarios) {
    // 类似于技术方向匹配的逻辑
    if (!scenarios || scenarios.length === 0) return null;

    let bestMatch = null;
    let highestScore = 0;

    for (const scenario of scenarios) {
      let score = 0;

      if (
        scenario.scenario_name.toLowerCase() === inputScenario.toLowerCase()
      ) {
        score += 100;
      }

      if (
        scenario.scenario_name
          .toLowerCase()
          .includes(inputScenario.toLowerCase()) ||
        inputScenario
          .toLowerCase()
          .includes(scenario.scenario_name.toLowerCase())
      ) {
        score += 50;
      }

      if (scenario.keywords) {
        const keywords = scenario.keywords.toLowerCase().split(",");
        if (
          keywords.some(
            (keyword) =>
              inputScenario.toLowerCase().includes(keyword.trim()) ||
              keyword.trim().includes(inputScenario.toLowerCase())
          )
        ) {
          score += 30;
        }
      }

      if (score > highestScore) {
        highestScore = score;
        bestMatch = scenario;
      }
    }

    return highestScore > 20 ? bestMatch : null;
  }

  /**
   * 初始化技术映射
   */
  _initTechMappings() {
    return {
      nlp: ["自然语言处理", "NLP", "文本处理"],
      cv: ["计算机视觉", "CV", "图像处理", "视觉"],
      推荐: ["推荐算法", "推荐系统"],
      搜索: ["搜索算法", "搜索引擎"],
      广告: ["广告算法", "广告投放"],
      多模态: ["多模态算法", "多模态学习"],
      大模型: ["LLM", "大语言模型", "GPT"],
      语音: ["语音识别", "语音合成", "ASR", "TTS"],
    };
  }

  /**
   * 初始化公司映射
   */
  _initCompanyMappings() {
    return {
      阿里: ["阿里巴巴", "阿里", "Alibaba"],
      腾讯: ["腾讯", "鹅厂", "Tencent"],
      字节: ["字节跳动", "字节", "ByteDance"],
      百度: ["百度", "Baidu"],
      美团: ["美团", "Meituan"],
    };
  }

  /**
   * 初始化职级映射
   */
  _initLevelMappings() {
    return {
      junior: { min: 4, max: 5 },
      senior: { min: 6, max: 7 },
      expert: { min: 7, max: 8 },
      principal: { min: 9, max: 10 },
    };
  }

}

// 创建单例实例
const infoExtractor = new InfoExtractor();

module.exports = infoExtractor;
