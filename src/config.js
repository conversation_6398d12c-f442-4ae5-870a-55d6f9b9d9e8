/**
 * Katrina-O3 配置管理模块
 * 统一管理环境变量和系统常量
 */

require('dotenv').config();

const config = {
  // 服务器配置
  server: {
    port: process.env.PORT || 3000,
    nodeEnv: process.env.NODE_ENV || 'development',
  },

  // 数据库配置
  database: {
    supabaseUrl: process.env.SUPABASE_URL,
    supabaseAnonKey: process.env.SUPABASE_ANON_KEY,
    supabaseServiceRoleKey: process.env.SUPABASE_SERVICE_ROLE_KEY,
  },

  // LLM 配置
  llm: {
    provider: process.env.LLM_PROVIDER || 'qwen',
    
    // Qwen 配置
    qwen: {
      apiKey: process.env.QWEN_API_KEY,
      endpoint: process.env.QWEN_ENDPOINT || 'https://dashscope.aliyuncs.com/api/v1/services/aigc/text-generation/generation',
      model: process.env.QWEN_MODEL || 'qwen-turbo',
    },

    // Claude 配置（预留）
    claude: {
      apiKey: process.env.CLAUDE_API_KEY,
      endpoint: process.env.CLAUDE_ENDPOINT || 'https://api.anthropic.com/v1/messages',
      model: process.env.CLAUDE_MODEL || 'claude-3-5-sonnet-20241022',
    },

    // 通用配置
    maxTokens: parseInt(process.env.LLM_MAX_TOKENS) || 2000,
    temperature: parseFloat(process.env.LLM_TEMPERATURE) || 0.7,
    timeout: parseInt(process.env.LLM_TIMEOUT) || 30000,
  },

  // 业务配置
  business: {
    // 对话历史条数限制
    maxHistoryMessages: parseInt(process.env.MAX_HISTORY_MESSAGES) || 10,
    
    // 推荐相关
    maxRecommendations: parseInt(process.env.MAX_RECOMMENDATIONS) || 4,
    
    // 信息收集必要字段
    requiredFields: [
      'current_company',
      'tech_direction', 
      'level_or_grade',
      'expected_compensation',
      'current_city_or_expected_city',
      'business_scenario'
    ],

    // 第一次推荐触发条件（满足其一）
    firstRecommendationTriggers: [
      ['current_company', 'tech_direction', 'level_or_grade'],
      ['current_company', 'tech_direction', 'expected_compensation'],
      ['current_company', 'tech_direction', 'level_or_grade', 'expected_compensation']
    ],

    // 第二次推荐触发条件
    secondRecommendationRequiredFields: [
      'business_scenario',
      'current_city_or_expected_city'
    ],

    // 4x4 公司类型配置
    companyTypes: {
      A: '头部大厂',
      B: '国企', 
      C: '中型公司',
      D: '创业型公司'
    },

    // 缺失类型补位策略
    fallbackStrategies: {
      'missing_A': ['B', 'C', 'D', 'B'],
      'missing_B': ['A', 'C', 'D', 'A'],
      'missing_AB': ['C', 'C', 'C', 'D'],
      'only_D': ['D', 'D', 'D', 'D']
    }
  },

  // 日志配置
  logging: {
    level: process.env.LOG_LEVEL || 'info',
    filePath: process.env.LOG_FILE_PATH || './logs/app.log',
  },

  // 安全配置
  security: {
    jwtSecret: process.env.JWT_SECRET || 'your_jwt_secret_change_this_in_production',
    rateLimitWindowMs: parseInt(process.env.RATE_LIMIT_WINDOW_MS) || 900000, // 15分钟
    rateLimitMaxRequests: parseInt(process.env.RATE_LIMIT_MAX_REQUESTS) || 100,
  },

  // 成本控制配置
  costControl: {
    dailyLimit: parseFloat(process.env.DAILY_COST_LIMIT) || 50,
    alertThreshold: parseFloat(process.env.COST_ALERT_THRESHOLD) || 0.8,
  }
};

// 配置验证
function validateConfig() {
  const errors = [];

  // 验证数据库配置
  if (!config.database.supabaseUrl) {
    errors.push('SUPABASE_URL is required');
  }
  if (!config.database.supabaseAnonKey) {
    errors.push('SUPABASE_ANON_KEY is required');
  }

  // 验证 LLM 配置
  if (config.llm.provider === 'qwen' && !config.llm.qwen.apiKey) {
    errors.push('QWEN_API_KEY is required when using qwen provider');
  }
  if (config.llm.provider === 'claude' && !config.llm.claude.apiKey) {
    errors.push('CLAUDE_API_KEY is required when using claude provider');
  }

  if (errors.length > 0) {
    throw new Error(`Configuration validation failed:\n${errors.join('\n')}`);
  }
}

// 开发环境下验证配置
if (config.server.nodeEnv === 'development') {
  try {
    validateConfig();
    console.log('✅ Configuration validation passed');
  } catch (error) {
    console.error('❌ Configuration validation failed:', error.message);
    process.exit(1);
  }
}

module.exports = config;
