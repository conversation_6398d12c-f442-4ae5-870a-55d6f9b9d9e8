/**
 * 简单的 Mock 测试
 */

// 设置环境
process.env.NODE_ENV = 'test';

// Mock
jest.mock('./src/llm-client');

const llmClient = require('./src/llm-client');

describe('Mock Test', () => {
  test('LLM Mock should work', async () => {
    console.log('llmClient:', llmClient);
    console.log('llmClient.provider:', llmClient.provider);
    
    const stats = llmClient.getStats();
    console.log('stats:', stats);
    
    expect(stats).toBeDefined();
    expect(stats.provider).toBe('mock');
  });
});
